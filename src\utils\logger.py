"""
日志工具模块
提供统一的日志记录功能，支持文件输出和控制台输出
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional


class Logger:
    """日志管理器"""
    
    _instance: Optional['Logger'] = None
    _initialized = False
    
    def __new__(cls) -> 'Logger':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._logger = logging.getLogger('MyApp')
            self._setup_logger()
            Logger._initialized = True
    
    def _setup_logger(self):
        """设置日志配置"""
        self._logger.setLevel(logging.DEBUG)
        
        # 清除现有的处理器
        for handler in self._logger.handlers[:]:
            self._logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self._logger.addHandler(console_handler)
        
        # 文件处理器
        self._setup_file_handler(formatter)
    
    def _setup_file_handler(self, formatter):
        """设置文件处理器"""
        try:
            # 创建日志目录
            log_dir = Path('logs')
            log_dir.mkdir(exist_ok=True)
            
            # 创建轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                log_dir / 'app.log',
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            self._logger.addHandler(file_handler)
            
        except Exception as e:
            self._logger.warning(f"无法创建文件日志处理器: {e}")
    
    def get_logger(self) -> logging.Logger:
        """获取日志记录器"""
        return self._logger
    
    def debug(self, message: str):
        """记录调试信息"""
        self._logger.debug(message)
    
    def info(self, message: str):
        """记录信息"""
        self._logger.info(message)
    
    def warning(self, message: str):
        """记录警告"""
        self._logger.warning(message)
    
    def error(self, message: str):
        """记录错误"""
        self._logger.error(message)
    
    def critical(self, message: str):
        """记录严重错误"""
        self._logger.critical(message)
    
    def exception(self, message: str):
        """记录异常信息（包含堆栈跟踪）"""
        self._logger.exception(message)


# 全局日志实例
logger = Logger()

# 便捷函数
def get_logger() -> logging.Logger:
    """获取日志记录器"""
    return logger.get_logger()

def debug(message: str):
    """记录调试信息"""
    logger.debug(message)

def info(message: str):
    """记录信息"""
    logger.info(message)

def warning(message: str):
    """记录警告"""
    logger.warning(message)

def error(message: str):
    """记录错误"""
    logger.error(message)

def critical(message: str):
    """记录严重错误"""
    logger.critical(message)

def exception(message: str):
    """记录异常信息（包含堆栈跟踪）"""
    logger.exception(message)
