2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - 已加载配置文件: config\app_config.json
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - 已加载配置文件: config\plugins_config.json
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - ==================================================
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - MyApp 启动中...
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - ==================================================
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - 开始初始化应用程序...
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - Qt应用程序创建成功
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - 全局异常处理器已安装
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - 配置加载完成: MyApp v1.0.0
2025-08-31 18:59:17 - MyApp - DEBUG - [logger.py:81] - 应用程序属性设置完成
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - 样式表加载成功
2025-08-31 18:59:17 - MyApp - DEBUG - [logger.py:81] - 发现插件: data_viewer
2025-08-31 18:59:17 - MyApp - DEBUG - [logger.py:81] - 发现插件: project_manager
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - 发现插件: ['data_viewer', 'project_manager']
2025-08-31 18:59:17 - MyApp - INFO - [logger.py:85] - 插件系统初始化完成
2025-08-31 18:59:17 - MyApp - DEBUG - [logger.py:81] - 创建可调整分割器
2025-08-31 18:59:17 - MyApp - DEBUG - [logger.py:81] - 侧边栏配置加载完成
2025-08-31 18:59:17 - MyApp - DEBUG - [logger.py:81] - 加载插件到侧边栏: []
2025-08-31 18:59:17 - MyApp - DEBUG - [logger.py:81] - 添加组件到分割器，索引: 0
2025-08-31 18:59:17 - MyApp - DEBUG - [logger.py:81] - 已订阅事件: activate_plugin_request
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 标签页切换到: 欢迎
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 添加标签页: 欢迎
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 添加组件到分割器，索引: 1
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 设置面板配置 0: min=50, threshold=125, default=300
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 设置面板配置 1: min=400, threshold=200, default=800
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 分割器配置完成
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 状态栏创建完成
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 主窗口UI初始化完成
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 已订阅事件: app_status_changed
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 已订阅事件: plugin_status_changed
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 事件连接完成
2025-08-31 18:59:18 - MyApp - DEBUG - [logger.py:81] - 窗口状态恢复完成
2025-08-31 18:59:18 - MyApp - INFO - [logger.py:85] - 主窗口创建完成
2025-08-31 18:59:18 - MyApp - INFO - [logger.py:85] - 主窗口创建成功
2025-08-31 18:59:18 - MyApp - INFO - [logger.py:85] - 应用程序初始化完成
2025-08-31 18:59:18 - MyApp - INFO - [logger.py:85] - 应用程序启动成功
2025-08-31 18:59:28 - MyApp - WARNING - [logger.py:89] - 插件不存在: project_manager
2025-08-31 18:59:29 - MyApp - WARNING - [logger.py:89] - 插件不存在: data_viewer
2025-08-31 18:59:29 - MyApp - WARNING - [logger.py:89] - 插件不存在: data_viewer
2025-08-31 18:59:30 - MyApp - WARNING - [logger.py:89] - 插件不存在: data_viewer
2025-08-31 18:59:30 - MyApp - WARNING - [logger.py:89] - 插件不存在: data_viewer
2025-08-31 18:59:31 - MyApp - INFO - [logger.py:85] - 已保存配置文件: config\app_config.json
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已设置配置: app.layout.main_splitter = {'sizes': [418, 1116], 'collapsed_states': [False, False], 'original_sizes': [300, 800]}
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 保存分割器状态: app.layout.main_splitter
2025-08-31 18:59:31 - MyApp - INFO - [logger.py:85] - 已保存配置文件: config\app_config.json
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已设置配置: app.window.geometry = {'x': 0, 'y': -7, 'width': 1536, 'height': 801, 'maximized': True}
2025-08-31 18:59:31 - MyApp - ERROR - [logger.py:93] - 发送事件失败 2event_emitted(QString,PyObject): maximum recursion depth exceeded while calling a Python object
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:31 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 18:59:32 - MyApp - DEBUG - [logger.py:81] - 已发送事件: main_window_closing
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - 主窗口正在关闭
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - ==================================================
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - MyApp 已退出
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - ==================================================
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - 开始关闭应用程序...
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - 已保存配置文件: config\app_config.json
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - 已保存配置文件: config\plugins_config.json
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - 已清除所有事件监听器
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - 全局异常处理器已卸载
2025-08-31 18:59:32 - MyApp - INFO - [logger.py:85] - 应用程序关闭完成
2025-08-31 19:01:15 - MyApp - INFO - [logger.py:85] - 已加载配置文件: config\app_config.json
2025-08-31 19:01:15 - MyApp - INFO - [logger.py:85] - 已加载配置文件: config\plugins_config.json
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已设置配置: test.value = test_data
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已订阅事件: test_event
2025-08-31 19:01:15 - MyApp - WARNING - [logger.py:89] - 事件信号递归，跳过: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已发送事件: test_event
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 已取消订阅事件: test_event
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 发现插件: data_viewer
2025-08-31 19:01:15 - MyApp - DEBUG - [logger.py:81] - 发现插件: project_manager
2025-08-31 19:01:15 - MyApp - ERROR - [logger.py:93] - 加载插件失败 project_manager: metaclass conflict: the metaclass of a derived class must be a (non-strict) subclass of the metaclasses of all its bases
2025-08-31 19:01:46 - MyApp - INFO - [logger.py:85] - 已加载配置文件: config\app_config.json
2025-08-31 19:01:46 - MyApp - INFO - [logger.py:85] - 已加载配置文件: config\plugins_config.json
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 插件配置加载完成: 项目管理
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 插件初始化完成: 项目管理
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 插件配置加载完成: 数据视图
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 插件初始化完成: 数据视图
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已设置配置: test.value = test_data
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已订阅事件: test_event
2025-08-31 19:01:46 - MyApp - WARNING - [logger.py:89] - 事件信号递归，跳过: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已发送事件: test_event
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 已取消订阅事件: test_event
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 发现插件: data_viewer
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 发现插件: project_manager
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 插件配置加载完成: 项目管理
2025-08-31 19:01:46 - MyApp - DEBUG - [logger.py:81] - 插件初始化完成: 项目管理
2025-08-31 19:01:46 - MyApp - INFO - [logger.py:85] - 插件加载成功: project_manager
2025-08-31 19:01:46 - MyApp - INFO - [logger.py:85] - [项目管理] 正在激活项目管理插件...
2025-08-31 19:02:39 - MyApp - INFO - [logger.py:85] - 已加载配置文件: config\app_config.json
2025-08-31 19:02:39 - MyApp - INFO - [logger.py:85] - 已加载配置文件: config\plugins_config.json
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 插件配置加载完成: 项目管理
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 插件初始化完成: 项目管理
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 插件配置加载完成: 数据视图
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 插件初始化完成: 数据视图
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已设置配置: test.value = test_data
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已订阅事件: test_event
2025-08-31 19:02:39 - MyApp - WARNING - [logger.py:89] - 事件信号递归，跳过: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: test_event
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已取消订阅事件: test_event
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 发现插件: data_viewer
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 发现插件: project_manager
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 插件配置加载完成: 项目管理
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 插件初始化完成: 项目管理
2025-08-31 19:02:39 - MyApp - INFO - [logger.py:85] - 插件加载成功: project_manager
2025-08-31 19:02:39 - MyApp - INFO - [logger.py:85] - [项目管理] 正在激活项目管理插件...
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已订阅事件: project_opened
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已订阅事件: project_closed
2025-08-31 19:02:39 - MyApp - INFO - [logger.py:85] - [项目管理] 插件激活
2025-08-31 19:02:39 - MyApp - WARNING - [logger.py:89] - 事件信号递归，跳过: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:39 - MyApp - DEBUG - [logger.py:81] - 已发送事件: plugin_ui_ready
2025-08-31 19:02:39 - MyApp - INFO - [logger.py:85] - [项目管理] 项目管理插件激活成功
2025-08-31 19:02:39 - MyApp - INFO - [logger.py:85] - 插件激活成功: project_manager
2025-08-31 19:02:40 - MyApp - WARNING - [logger.py:89] - 事件信号递归，跳过: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: plugin_activated
2025-08-31 19:02:40 - MyApp - INFO - [logger.py:85] - [项目管理] 正在停用项目管理插件...
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已取消订阅事件: project_opened
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已取消订阅事件: project_closed
2025-08-31 19:02:40 - MyApp - INFO - [logger.py:85] - [项目管理] 插件停用
2025-08-31 19:02:40 - MyApp - WARNING - [logger.py:89] - 事件信号递归，跳过: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:40 - MyApp - DEBUG - [logger.py:81] - 已发送事件: plugin_ui_cleanup
2025-08-31 19:02:40 - MyApp - INFO - [logger.py:85] - [项目管理] 项目管理插件停用成功
2025-08-31 19:02:40 - MyApp - INFO - [logger.py:85] - 插件停用成功: project_manager
2025-08-31 19:02:41 - MyApp - WARNING - [logger.py:89] - 事件信号递归，跳过: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: 2event_emitted(QString,PyObject)
2025-08-31 19:02:41 - MyApp - DEBUG - [logger.py:81] - 已发送事件: plugin_deactivated
