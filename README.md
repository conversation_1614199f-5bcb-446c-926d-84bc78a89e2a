# MyApp - 基于PySide6的高效软件框架

## 项目简介

这是一个基于PySide6开发的高效软件框架，采用插件系统进行功能实现和扩展。

## 特性

- 🔌 插件系统：仿照VSCode的插件机制，支持动态加载
- 🎨 现代UI：简洁直观的界面设计，支持多分辨率适配
- 📱 响应式布局：支持用户动态调整布局
- 🛠️ 模块化架构：高内聚、低耦合的设计原则
- 🔧 配置管理：集中化配置，支持动态更新
- 🚨 异常处理：全局异常捕获和优雅降级

## 项目结构

```
myapp/
├── src/                    # 源代码
│   ├── core/              # 核心模块
│   ├── ui/                # UI模块
│   ├── plugins/           # 插件目录
│   ├── utils/             # 工具模块
│   └── main.py           # 程序入口
├── config/               # 配置文件
├── resources/            # 资源文件
└── requirements.txt      # 依赖列表
```

## 安装和运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行程序：
```bash
python src/main.py
```

## 开发指南

### 插件开发

插件需要继承 `BasePlugin` 类并实现必要的接口方法。

### 配置管理

使用 `ConfigManager` 进行配置的读取和更新。

## 许可证

MIT License
