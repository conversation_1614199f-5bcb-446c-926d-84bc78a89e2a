"""
事件总线模块
提供模块间的事件通信机制，实现松耦合的架构
"""

from typing import Any, Callable, Dict, List, Optional
from PySide6.QtCore import QObject, Signal, QTimer
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import logger


class EventPriority(Enum):
    """事件优先级"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


@dataclass
class Event:
    """事件数据类"""
    name: str
    data: Any = None
    source: Optional[str] = None
    priority: EventPriority = EventPriority.NORMAL
    timestamp: Optional[float] = None


class EventBus(QObject):
    """事件总线"""
    
    # 通用事件信号
    event_emitted = Signal(str, object)  # (事件名, 事件数据)
    
    def __init__(self):
        super().__init__()
        self._listeners: Dict[str, List[Callable]] = {}
        self._one_time_listeners: Dict[str, List[Callable]] = {}
        self._event_queue: List[Event] = []
        self._processing = False
        
        # 定时器用于处理事件队列
        self._timer = QTimer()
        self._timer.timeout.connect(self._process_event_queue)
        self._timer.setSingleShot(True)
    
    def subscribe(self, event_name: str, callback: Callable, once: bool = False) -> bool:
        """
        订阅事件
        
        Args:
            event_name: 事件名称
            callback: 回调函数
            once: 是否只监听一次
            
        Returns:
            bool: 是否订阅成功
        """
        try:
            if once:
                if event_name not in self._one_time_listeners:
                    self._one_time_listeners[event_name] = []
                self._one_time_listeners[event_name].append(callback)
            else:
                if event_name not in self._listeners:
                    self._listeners[event_name] = []
                self._listeners[event_name].append(callback)
            
            logger.debug(f"已订阅事件: {event_name}")
            return True
            
        except Exception as e:
            logger.error(f"订阅事件失败 {event_name}: {e}")
            return False
    
    def unsubscribe(self, event_name: str, callback: Callable) -> bool:
        """
        取消订阅事件
        
        Args:
            event_name: 事件名称
            callback: 回调函数
            
        Returns:
            bool: 是否取消成功
        """
        try:
            # 从普通监听器中移除
            if event_name in self._listeners:
                if callback in self._listeners[event_name]:
                    self._listeners[event_name].remove(callback)
                    if not self._listeners[event_name]:
                        del self._listeners[event_name]
            
            # 从一次性监听器中移除
            if event_name in self._one_time_listeners:
                if callback in self._one_time_listeners[event_name]:
                    self._one_time_listeners[event_name].remove(callback)
                    if not self._one_time_listeners[event_name]:
                        del self._one_time_listeners[event_name]
            
            logger.debug(f"已取消订阅事件: {event_name}")
            return True
            
        except Exception as e:
            logger.error(f"取消订阅事件失败 {event_name}: {e}")
            return False
    
    def emit(self, event_name: str, data: Any = None, source: str = None, 
             priority: EventPriority = EventPriority.NORMAL, immediate: bool = False) -> bool:
        """
        发送事件
        
        Args:
            event_name: 事件名称
            data: 事件数据
            source: 事件源
            priority: 事件优先级
            immediate: 是否立即处理
            
        Returns:
            bool: 是否发送成功
        """
        try:
            import time
            event = Event(
                name=event_name,
                data=data,
                source=source,
                priority=priority,
                timestamp=time.time()
            )
            
            if immediate:
                self._handle_event(event)
            else:
                # 添加到队列并按优先级排序
                self._event_queue.append(event)
                self._event_queue.sort(key=lambda e: e.priority.value, reverse=True)
                
                # 启动处理定时器
                if not self._timer.isActive():
                    self._timer.start(0)
            
            # 发送Qt信号（避免递归）
            try:
                self.event_emitted.emit(event_name, data)
            except RecursionError:
                logger.warning(f"事件信号递归，跳过: {event_name}")
            
            logger.debug(f"已发送事件: {event_name}")
            return True
            
        except Exception as e:
            logger.error(f"发送事件失败 {event_name}: {e}")
            return False
    
    def _process_event_queue(self):
        """处理事件队列"""
        if self._processing or not self._event_queue:
            return
        
        self._processing = True
        
        try:
            # 处理队列中的所有事件
            while self._event_queue:
                event = self._event_queue.pop(0)
                self._handle_event(event)
                
        except Exception as e:
            logger.error(f"处理事件队列失败: {e}")
        finally:
            self._processing = False
    
    def _handle_event(self, event: Event):
        """处理单个事件"""
        try:
            # 处理普通监听器
            if event.name in self._listeners:
                for callback in self._listeners[event.name][:]:  # 复制列表避免修改问题
                    try:
                        callback(event)
                    except Exception as e:
                        logger.error(f"事件回调执行失败 {event.name}: {e}")
            
            # 处理一次性监听器
            if event.name in self._one_time_listeners:
                callbacks = self._one_time_listeners[event.name][:]
                del self._one_time_listeners[event.name]  # 清除一次性监听器
                
                for callback in callbacks:
                    try:
                        callback(event)
                    except Exception as e:
                        logger.error(f"一次性事件回调执行失败 {event.name}: {e}")
                        
        except Exception as e:
            logger.error(f"处理事件失败 {event.name}: {e}")
    
    def clear_listeners(self, event_name: Optional[str] = None):
        """
        清除监听器
        
        Args:
            event_name: 事件名称，为None时清除所有监听器
        """
        try:
            if event_name is None:
                self._listeners.clear()
                self._one_time_listeners.clear()
                logger.info("已清除所有事件监听器")
            else:
                if event_name in self._listeners:
                    del self._listeners[event_name]
                if event_name in self._one_time_listeners:
                    del self._one_time_listeners[event_name]
                logger.debug(f"已清除事件监听器: {event_name}")
                
        except Exception as e:
            logger.error(f"清除监听器失败: {e}")
    
    def get_listener_count(self, event_name: str) -> int:
        """获取事件监听器数量"""
        count = 0
        if event_name in self._listeners:
            count += len(self._listeners[event_name])
        if event_name in self._one_time_listeners:
            count += len(self._one_time_listeners[event_name])
        return count
    
    def get_all_events(self) -> List[str]:
        """获取所有已注册的事件名称"""
        events = set()
        events.update(self._listeners.keys())
        events.update(self._one_time_listeners.keys())
        return list(events)


# 全局事件总线实例
event_bus = EventBus()

# 便捷函数
def subscribe(event_name: str, callback: Callable, once: bool = False) -> bool:
    """订阅事件"""
    return event_bus.subscribe(event_name, callback, once)

def unsubscribe(event_name: str, callback: Callable) -> bool:
    """取消订阅事件"""
    return event_bus.unsubscribe(event_name, callback)

def emit(event_name: str, data: Any = None, source: str = None, 
         priority: EventPriority = EventPriority.NORMAL, immediate: bool = False) -> bool:
    """发送事件"""
    return event_bus.emit(event_name, data, source, priority, immediate)
