"""
插件管理模块
仿照VSCode插件系统，提供插件的动态加载、卸载和管理功能
"""

import importlib
import importlib.util
import sys
from pathlib import Path
from typing import Dict, List, Optional, Type, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

from PySide6.QtCore import QObject, Signal

from .config_manager import config_manager
from .event_bus import event_bus, EventPriority
from ..utils.logger import logger


class PluginState(Enum):
    """插件状态"""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class PluginInfo:
    """插件信息"""
    name: str
    version: str = "1.0.0"
    description: str = ""
    author: str = ""
    icon: str = ""
    dependencies: List[str] = None
    state: PluginState = PluginState.UNLOADED
    error_message: str = ""
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


# BasePlugin类已移动到 plugins/base_plugin.py 文件中


class PluginManager(QObject):
    """插件管理器"""
    
    # 插件状态变化信号
    plugin_loaded = Signal(str)  # 插件名
    plugin_activated = Signal(str)
    plugin_deactivated = Signal(str)
    plugin_error = Signal(str, str)  # 插件名, 错误信息
    
    def __init__(self, plugin_dir: str = "src/plugins"):
        super().__init__()
        self.plugin_dir = Path(plugin_dir)
        self._plugins: Dict[str, Any] = {}  # 存储插件实例
        self._plugin_info: Dict[str, PluginInfo] = {}
        self._plugin_modules: Dict[str, Any] = {}
        
        # 确保插件目录存在
        self.plugin_dir.mkdir(parents=True, exist_ok=True)
    
    def discover_plugins(self) -> List[str]:
        """发现可用的插件"""
        plugins = []
        
        try:
            for plugin_path in self.plugin_dir.iterdir():
                if plugin_path.is_dir() and not plugin_path.name.startswith('_'):
                    # 检查是否有__init__.py文件
                    init_file = plugin_path / "__init__.py"
                    if init_file.exists():
                        plugins.append(plugin_path.name)
                        logger.debug(f"发现插件: {plugin_path.name}")
            
        except Exception as e:
            logger.error(f"发现插件失败: {e}")
        
        return plugins
    
    def load_plugin(self, plugin_name: str) -> bool:
        """
        加载插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if plugin_name in self._plugins:
                logger.warning(f"插件已加载: {plugin_name}")
                return True
            
            # 更新插件状态
            self._update_plugin_state(plugin_name, PluginState.LOADING)
            
            # 构建插件模块路径
            plugin_path = self.plugin_dir / plugin_name
            if not plugin_path.exists():
                raise FileNotFoundError(f"插件目录不存在: {plugin_path}")
            
            # 动态导入插件模块
            module_name = f"src.plugins.{plugin_name}"
            
            if module_name in sys.modules:
                # 重新加载模块
                module = importlib.reload(sys.modules[module_name])
            else:
                # 首次加载模块
                spec = importlib.util.spec_from_file_location(
                    module_name, plugin_path / "__init__.py"
                )
                if spec is None or spec.loader is None:
                    raise ImportError(f"无法加载插件模块: {plugin_name}")
                
                module = importlib.util.module_from_spec(spec)
                sys.modules[module_name] = module
                spec.loader.exec_module(module)
            
            self._plugin_modules[plugin_name] = module
            
            # 查找插件类或实例
            plugin_class = self._find_plugin_class(module)
            plugin_instance = None

            if plugin_class is None:
                # 尝试查找插件实例
                if hasattr(module, 'plugin_instance'):
                    plugin_instance = module.plugin_instance
                else:
                    raise ValueError(f"插件中未找到插件类或实例: {plugin_name}")
            else:
                # 创建插件实例
                plugin_instance = plugin_class()

            # 设置管理器引用
            if hasattr(plugin_instance, 'set_manager'):
                plugin_instance.set_manager(self)
            elif hasattr(plugin_instance, 'manager'):
                plugin_instance.manager = self
            
            # 获取插件信息
            plugin_info = plugin_instance.get_info()
            plugin_info.state = PluginState.LOADED
            
            # 检查依赖
            if not self._check_dependencies(plugin_info):
                raise ValueError(f"插件依赖不满足: {plugin_name}")
            
            # 保存插件
            self._plugins[plugin_name] = plugin_instance
            self._plugin_info[plugin_name] = plugin_info
            
            logger.info(f"插件加载成功: {plugin_name}")
            self.plugin_loaded.emit(plugin_name)
            
            return True
            
        except Exception as e:
            error_msg = f"加载插件失败 {plugin_name}: {e}"
            logger.error(error_msg)
            self._update_plugin_state(plugin_name, PluginState.ERROR, str(e))
            self.plugin_error.emit(plugin_name, str(e))
            return False
    
    def _find_plugin_class(self, module) -> Optional[Type]:
        """在模块中查找插件类"""
        # 动态导入BasePlugin类以避免循环导入
        try:
            from ..plugins.base_plugin import BasePlugin
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and
                    issubclass(attr, BasePlugin) and
                    attr != BasePlugin):
                    return attr
        except ImportError:
            # 如果无法导入BasePlugin，查找具有必要方法的类
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and
                    hasattr(attr, 'get_info') and
                    hasattr(attr, 'activate') and
                    hasattr(attr, 'deactivate')):
                    return attr
        return None
    
    def _check_dependencies(self, plugin_info: PluginInfo) -> bool:
        """检查插件依赖"""
        for dep in plugin_info.dependencies:
            if dep not in self._plugins:
                logger.error(f"插件依赖未满足: {plugin_info.name} 需要 {dep}")
                return False
        return True
    
    def activate_plugin(self, plugin_name: str) -> bool:
        """激活插件"""
        try:
            if plugin_name not in self._plugins:
                logger.error(f"插件未加载: {plugin_name}")
                return False
            
            plugin = self._plugins[plugin_name]
            if plugin.is_active():
                logger.warning(f"插件已激活: {plugin_name}")
                return True
            
            # 激活插件
            if plugin.activate():
                plugin._active = True
                self._update_plugin_state(plugin_name, PluginState.ACTIVE)
                logger.info(f"插件激活成功: {plugin_name}")
                self.plugin_activated.emit(plugin_name)
                
                # 发送插件激活事件
                event_bus.emit("plugin_activated", {
                    "name": plugin_name,
                    "plugin": plugin
                }, source="PluginManager", priority=EventPriority.HIGH)
                
                return True
            else:
                logger.error(f"插件激活失败: {plugin_name}")
                return False
                
        except Exception as e:
            logger.error(f"激活插件失败 {plugin_name}: {e}")
            return False
    
    def deactivate_plugin(self, plugin_name: str) -> bool:
        """停用插件"""
        try:
            if plugin_name not in self._plugins:
                logger.error(f"插件未加载: {plugin_name}")
                return False
            
            plugin = self._plugins[plugin_name]
            if not plugin.is_active():
                logger.warning(f"插件未激活: {plugin_name}")
                return True
            
            # 停用插件
            if plugin.deactivate():
                plugin._active = False
                self._update_plugin_state(plugin_name, PluginState.LOADED)
                logger.info(f"插件停用成功: {plugin_name}")
                self.plugin_deactivated.emit(plugin_name)
                
                # 发送插件停用事件
                event_bus.emit("plugin_deactivated", {
                    "name": plugin_name,
                    "plugin": plugin
                }, source="PluginManager", priority=EventPriority.HIGH)
                
                return True
            else:
                logger.error(f"插件停用失败: {plugin_name}")
                return False
                
        except Exception as e:
            logger.error(f"停用插件失败 {plugin_name}: {e}")
            return False
    
    def _update_plugin_state(self, plugin_name: str, state: PluginState, error_msg: str = ""):
        """更新插件状态"""
        if plugin_name in self._plugin_info:
            self._plugin_info[plugin_name].state = state
            self._plugin_info[plugin_name].error_message = error_msg
        else:
            self._plugin_info[plugin_name] = PluginInfo(
                name=plugin_name, state=state, error_message=error_msg
            )
    
    def get_plugin(self, plugin_name: str) -> Optional[Any]:
        """获取插件实例"""
        return self._plugins.get(plugin_name)

    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """获取插件信息"""
        return self._plugin_info.get(plugin_name)

    def get_all_plugins(self) -> Dict[str, Any]:
        """获取所有插件"""
        return self._plugins.copy()

    def get_active_plugins(self) -> Dict[str, Any]:
        """获取所有激活的插件"""
        return {name: plugin for name, plugin in self._plugins.items()
                if hasattr(plugin, 'is_active') and plugin.is_active()}
    
    def load_enabled_plugins(self):
        """加载配置中启用的插件"""
        # 尝试不同的配置路径
        enabled_plugins = config_manager.get("plugins.enabled", [])
        if not enabled_plugins:
            # 尝试从plugins配置中读取
            plugins_config = config_manager.get_config("plugins")
            if plugins_config and "plugins" in plugins_config:
                enabled_plugins = plugins_config["plugins"].get("enabled", [])

        load_order = config_manager.get("plugins.load_order", enabled_plugins)
        if not load_order:
            plugins_config = config_manager.get_config("plugins")
            if plugins_config and "plugins" in plugins_config:
                load_order = plugins_config["plugins"].get("load_order", enabled_plugins)

        logger.info(f"开始加载启用的插件: {enabled_plugins}")

        # 按加载顺序加载插件
        for plugin_name in load_order:
            if plugin_name in enabled_plugins:
                logger.info(f"正在加载插件: {plugin_name}")
                if self.load_plugin(plugin_name):
                    if self.activate_plugin(plugin_name):
                        logger.info(f"插件 {plugin_name} 加载并激活成功")
                    else:
                        logger.error(f"插件 {plugin_name} 激活失败")
                else:
                    logger.error(f"插件 {plugin_name} 加载失败")


# 全局插件管理器实例
plugin_manager = PluginManager()
