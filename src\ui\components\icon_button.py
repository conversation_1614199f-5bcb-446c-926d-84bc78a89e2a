"""
图标按钮组件
用于侧边栏的图标按钮，支持悬停和选中状态
"""

from typing import Optional
from PySide6.QtWidgets import QPushButton, QSizePolicy
from PySide6.QtCore import Qt, Signal, QSize, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QIcon, QPainter, QColor, QBrush, QPen

from ...utils.logger import logger


class IconButton(QPushButton):
    """图标按钮组件"""
    
    # 自定义信号
    toggled_with_name = Signal(str, bool)  # (按钮名称, 是否选中)
    
    def __init__(self, name: str = "", icon_path: str = "", tooltip: str = "", parent=None):
        super().__init__(parent)
        
        self._name = name
        self._icon_path = icon_path
        self._is_active = False
        self._hover_opacity = 0.0
        
        # 设置基本属性
        self.setObjectName("iconButton")
        self.setCheckable(True)
        self.setToolTip(tooltip or name)
        
        # 设置尺寸策略
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        
        # 设置图标
        if icon_path:
            self.setIcon(QIcon(icon_path))
        
        # 连接信号
        self.toggled.connect(self._on_toggled)
        
        # 设置样式
        self._setup_style()
        
        # 创建动画
        self._setup_animations()
        
        logger.debug(f"创建图标按钮: {name}")
    
    def _setup_style(self):
        """设置按钮样式"""
        # 基础样式通过QSS设置，这里设置一些基本属性
        self.setFlat(True)
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)
    
    def _setup_animations(self):
        """设置动画效果"""
        # 悬停动画（暂时禁用，使用简单的状态切换）
        # self._hover_animation = QPropertyAnimation(self, b"hover_opacity")
        # self._hover_animation.setDuration(150)
        # self._hover_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        pass
    
    def _on_toggled(self, checked: bool):
        """处理按钮切换事件"""
        self._is_active = checked
        self.toggled_with_name.emit(self._name, checked)
        self.update()  # 触发重绘
    
    def set_active(self, active: bool):
        """设置按钮激活状态"""
        if self._is_active != active:
            self._is_active = active
            self.setChecked(active)
            self.update()
    
    def is_active(self) -> bool:
        """获取按钮激活状态"""
        return self._is_active
    
    def get_name(self) -> str:
        """获取按钮名称"""
        return self._name
    
    def set_icon_path(self, icon_path: str):
        """设置图标路径"""
        self._icon_path = icon_path
        if icon_path:
            self.setIcon(QIcon(icon_path))
        else:
            self.setIcon(QIcon())
    
    def get_icon_path(self) -> str:
        """获取图标路径"""
        return self._icon_path
    
    def sizeHint(self) -> QSize:
        """返回建议尺寸"""
        return QSize(46, 46)  # 50px - 4px边距
    
    def minimumSizeHint(self) -> QSize:
        """返回最小尺寸"""
        return QSize(46, 46)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        if not self._is_active:
            self._start_hover_animation(True)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        if not self._is_active:
            self._start_hover_animation(False)
    
    def _start_hover_animation(self, hover: bool):
        """开始悬停动画"""
        target_opacity = 0.3 if hover else 0.0
        self._hover_opacity = target_opacity
        self.update()  # 直接更新，不使用动画
    
    def get_hover_opacity(self) -> float:
        """获取悬停透明度（用于动画）"""
        return self._hover_opacity
    
    def set_hover_opacity(self, opacity: float):
        """设置悬停透明度（用于动画）"""
        self._hover_opacity = opacity
        self.update()
    
    # 定义属性用于动画
    hover_opacity = property(get_hover_opacity, set_hover_opacity)
    
    def paintEvent(self, event):
        """自定义绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        
        # 绘制背景
        if self._is_active:
            # 激活状态背景
            painter.setBrush(QBrush(QColor(0, 122, 204)))  # #007acc
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawRoundedRect(rect.adjusted(2, 2, -2, -2), 4, 4)
        elif self._hover_opacity > 0:
            # 悬停状态背景
            color = QColor(62, 62, 66)  # #3e3e42
            color.setAlphaF(self._hover_opacity)
            painter.setBrush(QBrush(color))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawRoundedRect(rect.adjusted(2, 2, -2, -2), 4, 4)
        
        # 绘制图标
        icon = self.icon()
        if not icon.isNull():
            icon_size = QSize(24, 24)
            icon_rect = rect
            icon_rect.setSize(icon_size)
            icon_rect.moveCenter(rect.center())
            
            # 根据状态设置图标模式
            mode = QIcon.Mode.Normal
            if not self.isEnabled():
                mode = QIcon.Mode.Disabled
            elif self._is_active:
                mode = QIcon.Mode.Selected
            
            icon.paint(painter, icon_rect, Qt.AlignmentFlag.AlignCenter, mode)
        
        painter.end()


class IconButtonGroup:
    """图标按钮组，管理多个图标按钮的互斥选择"""
    
    def __init__(self):
        self._buttons: list[IconButton] = []
        self._current_button: Optional[IconButton] = None
    
    def add_button(self, button: IconButton):
        """添加按钮到组"""
        if button not in self._buttons:
            self._buttons.append(button)
            button.toggled_with_name.connect(self._on_button_toggled)
            logger.debug(f"添加按钮到组: {button.get_name()}")
    
    def remove_button(self, button: IconButton):
        """从组中移除按钮"""
        if button in self._buttons:
            self._buttons.remove(button)
            button.toggled_with_name.disconnect(self._on_button_toggled)
            if self._current_button == button:
                self._current_button = None
            logger.debug(f"从组中移除按钮: {button.get_name()}")
    
    def _on_button_toggled(self, name: str, checked: bool):
        """处理按钮切换事件"""
        button = self._find_button_by_name(name)
        if not button:
            return
        
        if checked:
            # 如果是同一个按钮再次点击，则折叠
            if self._current_button == button:
                button.set_active(False)
                self._current_button = None
            else:
                # 取消其他按钮的选中状态
                if self._current_button:
                    self._current_button.set_active(False)
                
                # 设置当前按钮
                self._current_button = button
                button.set_active(True)
        else:
            if self._current_button == button:
                self._current_button = None
    
    def _find_button_by_name(self, name: str) -> Optional[IconButton]:
        """根据名称查找按钮"""
        for button in self._buttons:
            if button.get_name() == name:
                return button
        return None
    
    def get_current_button(self) -> Optional[IconButton]:
        """获取当前选中的按钮"""
        return self._current_button
    
    def set_current_button(self, name: str):
        """设置当前选中的按钮"""
        button = self._find_button_by_name(name)
        if button:
            # 模拟点击事件
            button.setChecked(True)
    
    def clear_selection(self):
        """清除所有选择"""
        if self._current_button:
            self._current_button.set_active(False)
            self._current_button = None
    
    def get_all_buttons(self) -> list[IconButton]:
        """获取所有按钮"""
        return self._buttons.copy()
