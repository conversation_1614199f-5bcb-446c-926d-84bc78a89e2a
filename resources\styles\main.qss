/* 主窗口样式 */
QMainWindow {
    background-color: #f5f5f5;
    color: #333333;
}

/* 侧边栏样式 */
#sidebar {
    background-color: #2d2d30;
    border-right: 1px solid #3e3e42;
}

/* 图标栏样式 */
#iconBar {
    background-color: #2d2d30;
    border-right: 1px solid #3e3e42;
}

/* 图标按钮样式 */
#iconButton {
    background-color: transparent;
    border: none;
    padding: 8px;
    margin: 2px;
    border-radius: 4px;
}

#iconButton:hover {
    background-color: #3e3e42;
}

#iconButton:checked {
    background-color: #007acc;
}

/* 内容栏样式 */
#contentArea {
    background-color: #252526;
    border: none;
}

/* 工作区样式 */
#workspace {
    background-color: #ffffff;
    border: 1px solid #e1e1e1;
}

/* 标签页样式 */
QTabWidget::pane {
    border: 1px solid #e1e1e1;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #f3f3f3;
    border: 1px solid #e1e1e1;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    border-bottom: 1px solid #ffffff;
}

QTabBar::tab:hover {
    background-color: #e8e8e8;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #e1e1e1;
    width: 2px;
}

QSplitter::handle:hover {
    background-color: #007acc;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #f1f1f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #c1c1c1;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a8a8a8;
}
