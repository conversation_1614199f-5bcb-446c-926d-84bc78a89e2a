"""
工作区模块
实现中间工作区的标签页管理和内容显示
"""

from typing import Dict, Optional, List
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QTabWidget, QLabel, 
                               QHBoxLayout, QPushButton, QFrame, QScrollArea)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QIcon

from ..core.plugin_manager import plugin_manager
from ..core.config_manager import config_manager
from ..core.event_bus import event_bus
from ..utils.logger import logger


class TabBar(QTabWidget):
    """自定义标签栏"""
    
    # 信号
    tab_close_requested_with_name = Signal(str)  # 标签名称
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置标签栏属性
        self.setTabsClosable(True)
        self.setMovable(True)
        self.setDocumentMode(True)
        
        # 连接信号
        self.tabCloseRequested.connect(self._on_tab_close_requested)
        
    def _on_tab_close_requested(self, index: int):
        """处理标签关闭请求"""
        tab_name = self.tabText(index)
        self.tab_close_requested_with_name.emit(tab_name)


class WelcomeWidget(QWidget):
    """欢迎页面组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("欢迎使用 MyApp")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 副标题
        subtitle_label = QLabel("基于PySide6的高效软件框架")
        subtitle_font = QFont()
        subtitle_font.setPointSize(14)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("color: #666666;")
        
        # 功能介绍
        features_widget = self._create_features_widget()
        
        # 快速开始
        quick_start_widget = self._create_quick_start_widget()
        
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addSpacing(30)
        layout.addWidget(features_widget)
        layout.addSpacing(20)
        layout.addWidget(quick_start_widget)
        layout.addStretch()
    
    def _create_features_widget(self) -> QWidget:
        """创建功能介绍组件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        features_label = QLabel("主要特性")
        features_font = QFont()
        features_font.setPointSize(16)
        features_font.setBold(True)
        features_label.setFont(features_font)
        features_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        features_list = [
            "🔌 插件系统：支持动态加载和扩展",
            "🎨 现代UI：简洁直观的界面设计",
            "📱 响应式布局：支持多分辨率适配",
            "🛠️ 模块化架构：高内聚、低耦合",
            "🔧 配置管理：集中化配置系统",
            "🚨 异常处理：全局异常捕获机制"
        ]
        
        layout.addWidget(features_label)
        layout.addSpacing(10)
        
        for feature in features_list:
            feature_label = QLabel(feature)
            feature_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(feature_label)
        
        return widget
    
    def _create_quick_start_widget(self) -> QWidget:
        """创建快速开始组件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        quick_start_label = QLabel("快速开始")
        quick_start_font = QFont()
        quick_start_font.setPointSize(16)
        quick_start_font.setBold(True)
        quick_start_label.setFont(quick_start_font)
        quick_start_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 项目管理按钮
        project_btn = QPushButton("项目管理")
        project_btn.clicked.connect(lambda: self._activate_plugin("project_manager"))
        
        # 数据视图按钮
        data_btn = QPushButton("数据视图")
        data_btn.clicked.connect(lambda: self._activate_plugin("data_viewer"))
        
        button_layout.addWidget(project_btn)
        button_layout.addWidget(data_btn)
        
        layout.addWidget(quick_start_label)
        layout.addSpacing(10)
        layout.addLayout(button_layout)
        
        return widget
    
    def _activate_plugin(self, plugin_name: str):
        """激活插件"""
        try:
            if plugin_manager.get_plugin(plugin_name):
                event_bus.emit("activate_plugin_request", plugin_name)
            else:
                logger.warning(f"插件不存在: {plugin_name}")
        except Exception as e:
            logger.error(f"激活插件失败: {e}")


class PluginContentWidget(QWidget):
    """插件内容组件"""
    
    def __init__(self, plugin_name: str, parent=None):
        super().__init__(parent)
        
        self._plugin_name = plugin_name
        self._plugin_widget: Optional[QWidget] = None
        
        self._init_ui()
        self._load_plugin_content()
    
    def _init_ui(self):
        """初始化UI"""
        self._layout = QVBoxLayout(self)
        self._layout.setContentsMargins(0, 0, 0, 0)
        self._layout.setSpacing(0)
    
    def _load_plugin_content(self):
        """加载插件内容"""
        try:
            plugin = plugin_manager.get_plugin(self._plugin_name)
            if plugin:
                # 获取插件的工作区组件
                plugin_widget = plugin.get_widget()
                if plugin_widget:
                    # 如果插件提供了工作区组件，使用它
                    self._plugin_widget = plugin_widget
                    self._layout.addWidget(self._plugin_widget)
                else:
                    # 否则创建默认内容
                    self._create_default_content()
            else:
                self._create_error_content()
                
        except Exception as e:
            logger.error(f"加载插件内容失败 {self._plugin_name}: {e}")
            self._create_error_content()
    
    def _create_default_content(self):
        """创建默认内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        plugin_info = plugin_manager.get_plugin_info(self._plugin_name)
        if plugin_info:
            title = plugin_info.name
            description = plugin_info.description
        else:
            title = self._plugin_name
            description = "插件内容"
        
        title_label = QLabel(title)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #666666;")
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        self._layout.addWidget(widget)
    
    def _create_error_content(self):
        """创建错误内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        error_label = QLabel("插件加载失败")
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_label.setStyleSheet("color: #ff0000;")
        
        plugin_label = QLabel(f"插件: {self._plugin_name}")
        plugin_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        plugin_label.setStyleSheet("color: #666666;")
        
        layout.addWidget(error_label)
        layout.addWidget(plugin_label)
        
        self._layout.addWidget(widget)
    
    def get_plugin_name(self) -> str:
        """获取插件名称"""
        return self._plugin_name


class Workspace(QWidget):
    """工作区主组件"""
    
    # 信号
    tab_changed = Signal(str)  # 标签名称
    tab_closed = Signal(str)   # 标签名称
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._tab_widget: TabBar = None
        self._tabs: Dict[str, QWidget] = {}
        self._active_plugin: Optional[str] = None
        
        self._init_ui()
        self._connect_events()
        
        # 添加欢迎标签
        self._add_welcome_tab()
    
    def _init_ui(self):
        """初始化UI"""
        self.setObjectName("workspace")
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建标签组件
        self._tab_widget = TabBar()
        self._tab_widget.currentChanged.connect(self._on_tab_changed)
        self._tab_widget.tab_close_requested_with_name.connect(self._on_tab_close_requested)
        
        layout.addWidget(self._tab_widget)
    
    def _connect_events(self):
        """连接事件"""
        try:
            # 订阅插件激活事件
            event_bus.subscribe("activate_plugin_request", self._on_activate_plugin_request)
            
            # 订阅插件状态变化事件
            plugin_manager.plugin_activated.connect(self._on_plugin_activated)
            plugin_manager.plugin_deactivated.connect(self._on_plugin_deactivated)
            
        except Exception as e:
            logger.error(f"连接工作区事件失败: {e}")
    
    def _add_welcome_tab(self):
        """添加欢迎标签"""
        welcome_widget = WelcomeWidget()
        self.add_tab("欢迎", welcome_widget, closable=False)
        self.set_active_tab("欢迎")
    
    def add_tab(self, name: str, widget: QWidget, closable: bool = True):
        """添加标签页"""
        if name in self._tabs:
            # 如果标签已存在，激活它
            self.set_active_tab(name)
            return
        
        try:
            # 添加到标签组件
            index = self._tab_widget.addTab(widget, name)
            
            # 设置是否可关闭
            if not closable:
                # 隐藏关闭按钮（通过样式或其他方式）
                pass
            
            # 保存引用
            self._tabs[name] = widget
            
            logger.debug(f"添加标签页: {name}")
            
        except Exception as e:
            logger.error(f"添加标签页失败 {name}: {e}")
    
    def remove_tab(self, name: str):
        """移除标签页"""
        if name not in self._tabs or name == "欢迎":
            return
        
        try:
            widget = self._tabs[name]
            index = self._tab_widget.indexOf(widget)
            
            if index >= 0:
                self._tab_widget.removeTab(index)
                del self._tabs[name]
                
                # 如果移除的是当前激活的插件标签，清除激活状态
                if self._active_plugin == name:
                    self._active_plugin = None
                
                logger.debug(f"移除标签页: {name}")
                
        except Exception as e:
            logger.error(f"移除标签页失败 {name}: {e}")
    
    def set_active_tab(self, name: str):
        """设置激活的标签页"""
        if name not in self._tabs:
            return
        
        try:
            widget = self._tabs[name]
            index = self._tab_widget.indexOf(widget)
            
            if index >= 0:
                self._tab_widget.setCurrentIndex(index)
                
        except Exception as e:
            logger.error(f"设置激活标签页失败 {name}: {e}")
    
    def _on_tab_changed(self, index: int):
        """处理标签变化事件"""
        if index >= 0:
            widget = self._tab_widget.widget(index)
            tab_name = self._tab_widget.tabText(index)
            
            # 发送信号
            self.tab_changed.emit(tab_name)
            
            logger.debug(f"标签页切换到: {tab_name}")
    
    def _on_tab_close_requested(self, tab_name: str):
        """处理标签关闭请求"""
        if tab_name != "欢迎":  # 欢迎标签不能关闭
            self.remove_tab(tab_name)
            self.tab_closed.emit(tab_name)
    
    def _on_activate_plugin_request(self, event):
        """处理插件激活请求"""
        if hasattr(event, 'data'):
            plugin_name = event.data
            self.set_active_plugin(plugin_name)
    
    def _on_plugin_activated(self, plugin_name: str):
        """处理插件激活事件"""
        # 插件激活时不自动添加标签，等待用户手动切换
        pass
    
    def _on_plugin_deactivated(self, plugin_name: str):
        """处理插件停用事件"""
        # 移除对应的标签页
        self.remove_tab(plugin_name)
    
    def set_active_plugin(self, plugin_name: str):
        """设置激活的插件"""
        try:
            self._active_plugin = plugin_name
            
            # 如果标签不存在，创建它
            if plugin_name not in self._tabs:
                plugin_widget = PluginContentWidget(plugin_name)
                
                # 获取插件信息作为标签名
                plugin_info = plugin_manager.get_plugin_info(plugin_name)
                tab_name = plugin_info.name if plugin_info else plugin_name
                
                self.add_tab(tab_name, plugin_widget)
            
            # 激活标签
            plugin_info = plugin_manager.get_plugin_info(plugin_name)
            tab_name = plugin_info.name if plugin_info else plugin_name
            self.set_active_tab(tab_name)
            
        except Exception as e:
            logger.error(f"设置激活插件失败: {e}")
    
    def get_active_plugin(self) -> Optional[str]:
        """获取当前激活的插件"""
        return self._active_plugin
    
    def get_current_tab_name(self) -> Optional[str]:
        """获取当前标签名称"""
        current_index = self._tab_widget.currentIndex()
        if current_index >= 0:
            return self._tab_widget.tabText(current_index)
        return None
    
    def get_tab_count(self) -> int:
        """获取标签数量"""
        return self._tab_widget.count()
    
    def clear_plugin_tabs(self):
        """清除所有插件标签页"""
        plugin_tabs = [name for name in self._tabs.keys() if name != "欢迎"]
        for tab_name in plugin_tabs:
            self.remove_tab(tab_name)
