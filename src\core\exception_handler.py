"""
异常处理模块
提供全局异常捕获和处理机制，避免程序崩溃
"""

import sys
import traceback
from typing import Callable, Optional, Any
from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QMessageBox, QApplication

from ..utils.logger import logger


class ExceptionHandler(QObject):
    """全局异常处理器"""
    
    # 异常信号
    exception_occurred = Signal(str, str)  # (异常类型, 异常信息)
    
    def __init__(self):
        super().__init__()
        self._original_excepthook = sys.excepthook
        self._error_callback: Optional[Callable] = None
        
    def install(self):
        """安装全局异常处理器"""
        sys.excepthook = self._handle_exception
        logger.info("全局异常处理器已安装")
    
    def uninstall(self):
        """卸载全局异常处理器"""
        sys.excepthook = self._original_excepthook
        logger.info("全局异常处理器已卸载")
    
    def set_error_callback(self, callback: Callable[[str, str], None]):
        """设置错误回调函数"""
        self._error_callback = callback
    
    def _handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许 Ctrl+C 正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 获取异常信息
        error_msg = str(exc_value)
        error_type = exc_type.__name__
        
        # 获取详细的堆栈跟踪
        tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
        full_traceback = ''.join(tb_lines)
        
        # 记录到日志
        logger.critical(f"未捕获的异常: {error_type}: {error_msg}")
        logger.critical(f"堆栈跟踪:\n{full_traceback}")
        
        # 发送异常信号
        self.exception_occurred.emit(error_type, error_msg)
        
        # 调用错误回调
        if self._error_callback:
            try:
                self._error_callback(error_type, error_msg)
            except Exception as e:
                logger.error(f"错误回调函数执行失败: {e}")
        
        # 显示错误对话框
        self._show_error_dialog(error_type, error_msg, full_traceback)
    
    def _show_error_dialog(self, error_type: str, error_msg: str, traceback_info: str):
        """显示错误对话框"""
        try:
            app = QApplication.instance()
            if app is None:
                return
            
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setWindowTitle("程序错误")
            msg_box.setText(f"程序遇到了一个未处理的错误：\n\n{error_type}: {error_msg}")
            msg_box.setDetailedText(traceback_info)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.exec()
            
        except Exception as e:
            logger.error(f"显示错误对话框失败: {e}")


def safe_execute(func: Callable, *args, **kwargs) -> tuple[bool, Any]:
    """
    安全执行函数，捕获异常并返回结果
    
    Args:
        func: 要执行的函数
        *args: 位置参数
        **kwargs: 关键字参数
    
    Returns:
        tuple: (是否成功, 结果或异常信息)
    """
    try:
        result = func(*args, **kwargs)
        return True, result
    except Exception as e:
        logger.exception(f"执行函数 {func.__name__} 时发生异常")
        return False, str(e)


def handle_exceptions(show_dialog: bool = True):
    """
    装饰器：为函数添加异常处理
    
    Args:
        show_dialog: 是否显示错误对话框
    """
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"函数 {func.__name__} 执行异常")
                
                if show_dialog:
                    try:
                        app = QApplication.instance()
                        if app:
                            msg_box = QMessageBox()
                            msg_box.setIcon(QMessageBox.Icon.Warning)
                            msg_box.setWindowTitle("操作失败")
                            msg_box.setText(f"操作失败：{str(e)}")
                            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                            msg_box.exec()
                    except:
                        pass
                
                return None
        return wrapper
    return decorator


# 全局异常处理器实例
exception_handler = ExceptionHandler()
