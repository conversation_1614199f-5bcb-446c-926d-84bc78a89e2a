"""
侧边栏模块
实现侧边栏的图标栏、内容栏、折叠展开机制
"""

from typing import Dict, Optional
from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, 
                               QStackedWidget, QLabel, QFrame)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont

from .components.icon_button import IconButton, IconButtonGroup
from ..core.plugin_manager import plugin_manager
from ..core.config_manager import config_manager
from ..utils.logger import logger


class IconBar(QWidget):
    """图标栏组件"""
    
    # 信号
    icon_clicked = Signal(str)  # 图标名称
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._button_group = IconButtonGroup()
        self._buttons: Dict[str, IconButton] = {}
        
        self._init_ui()
        
    def _init_ui(self):
        """初始化UI"""
        self.setObjectName("iconBar")
        self.setFixedWidth(50)
        
        # 创建垂直布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 8, 2, 8)
        layout.setSpacing(4)
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # 添加弹性空间
        layout.addStretch()
    
    def add_icon(self, name: str, icon_path: str = "", tooltip: str = ""):
        """添加图标按钮"""
        if name in self._buttons:
            logger.warning(f"图标按钮已存在: {name}")
            return
        
        # 创建按钮
        button = IconButton(name, icon_path, tooltip)
        button.toggled_with_name.connect(self._on_icon_clicked)
        
        # 添加到组和字典
        self._button_group.add_button(button)
        self._buttons[name] = button
        
        # 添加到布局（在弹性空间之前）
        layout = self.layout()
        layout.insertWidget(layout.count() - 1, button)
        
        logger.debug(f"添加图标按钮: {name}")
    
    def remove_icon(self, name: str):
        """移除图标按钮"""
        if name not in self._buttons:
            return
        
        button = self._buttons[name]
        
        # 从组和字典中移除
        self._button_group.remove_button(button)
        del self._buttons[name]
        
        # 从布局中移除
        self.layout().removeWidget(button)
        button.deleteLater()
        
        logger.debug(f"移除图标按钮: {name}")
    
    def _on_icon_clicked(self, name: str, checked: bool):
        """处理图标点击事件"""
        self.icon_clicked.emit(name)
    
    def set_active_icon(self, name: str):
        """设置激活的图标"""
        self._button_group.set_current_button(name)
    
    def clear_selection(self):
        """清除选择"""
        self._button_group.clear_selection()
    
    def get_current_icon(self) -> Optional[str]:
        """获取当前选中的图标"""
        current_button = self._button_group.get_current_button()
        return current_button.get_name() if current_button else None


class ContentArea(QWidget):
    """内容区域组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._stacked_widget: QStackedWidget = None
        self._content_widgets: Dict[str, QWidget] = {}
        self._current_content: Optional[str] = None
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        self.setObjectName("contentArea")
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建堆叠组件
        self._stacked_widget = QStackedWidget()
        layout.addWidget(self._stacked_widget)
        
        # 添加默认页面
        self._add_default_page()
    
    def _add_default_page(self):
        """添加默认页面"""
        default_widget = QWidget()
        default_layout = QVBoxLayout(default_widget)
        default_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 添加欢迎标签
        welcome_label = QLabel("欢迎使用 MyApp")
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = QFont()
        font.setPointSize(12)
        font.setBold(True)
        welcome_label.setFont(font)
        
        info_label = QLabel("请选择左侧的功能模块")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        default_layout.addWidget(welcome_label)
        default_layout.addWidget(info_label)
        
        self._stacked_widget.addWidget(default_widget)
        self._content_widgets["default"] = default_widget
    
    def add_content(self, name: str, widget: QWidget):
        """添加内容组件"""
        if name in self._content_widgets:
            logger.warning(f"内容组件已存在: {name}")
            return
        
        self._stacked_widget.addWidget(widget)
        self._content_widgets[name] = widget
        
        logger.debug(f"添加内容组件: {name}")
    
    def remove_content(self, name: str):
        """移除内容组件"""
        if name not in self._content_widgets or name == "default":
            return
        
        widget = self._content_widgets[name]
        self._stacked_widget.removeWidget(widget)
        del self._content_widgets[name]
        
        # 如果当前显示的是被移除的内容，切换到默认页面
        if self._current_content == name:
            self.show_content("default")
        
        logger.debug(f"移除内容组件: {name}")
    
    def show_content(self, name: str):
        """显示指定内容"""
        if name not in self._content_widgets:
            logger.warning(f"内容组件不存在: {name}")
            return
        
        widget = self._content_widgets[name]
        self._stacked_widget.setCurrentWidget(widget)
        self._current_content = name
        
        logger.debug(f"显示内容: {name}")
    
    def get_current_content(self) -> Optional[str]:
        """获取当前显示的内容"""
        return self._current_content
    
    def clear_content(self):
        """清除内容，显示默认页面"""
        self.show_content("default")


class Sidebar(QWidget):
    """侧边栏主组件"""
    
    # 信号
    content_changed = Signal(str)  # 内容变化
    collapsed_state_changed = Signal(bool)  # 折叠状态变化
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._icon_bar: IconBar = None
        self._content_area: ContentArea = None
        self._collapsed = False
        self._animation: Optional[QPropertyAnimation] = None
        
        # 配置
        self._icon_width = 50
        self._min_content_width = 125
        self._default_width = 300
        
        self._init_ui()
        self._load_config()

        # 订阅插件激活事件，动态加载插件
        from ..core.plugin_manager import plugin_manager
        plugin_manager.plugin_activated.connect(self._on_plugin_activated_for_sidebar)

        # 延迟加载插件，等待插件系统初始化完成
        from PySide6.QtCore import QTimer
        QTimer.singleShot(500, self._load_plugins)  # 增加延迟时间
    
    def _init_ui(self):
        """初始化UI"""
        self.setObjectName("sidebar")

        # 设置最小尺寸，确保侧边栏可见
        self.setMinimumWidth(50)  # 至少显示图标栏

        # 创建水平布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建图标栏
        self._icon_bar = IconBar()
        self._icon_bar.icon_clicked.connect(self._on_icon_clicked)
        layout.addWidget(self._icon_bar)
        
        # 创建分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setFixedWidth(1)
        layout.addWidget(separator)
        
        # 创建内容区域
        self._content_area = ContentArea()
        layout.addWidget(self._content_area)

        logger.debug(f"侧边栏UI初始化完成，图标栏宽度: {self._icon_bar.width()}")
    
    def _load_config(self):
        """加载配置"""
        try:
            sidebar_config = config_manager.get("app.sidebar", {})
            self._icon_width = sidebar_config.get("icon_width", 50)
            self._min_content_width = sidebar_config.get("min_content_width", 125)
            self._default_width = sidebar_config.get("default_width", 300)
            self._collapsed = sidebar_config.get("collapsed", False)
            
            # 应用配置
            self._icon_bar.setFixedWidth(self._icon_width)
            
            logger.debug("侧边栏配置加载完成")
            
        except Exception as e:
            logger.error(f"加载侧边栏配置失败: {e}")
    
    def _on_plugin_activated_for_sidebar(self, plugin_name: str):
        """处理插件激活事件（用于侧边栏）"""
        try:
            plugin = plugin_manager.get_plugin(plugin_name)
            if plugin:
                self._add_plugin_to_sidebar(plugin_name, plugin)
                logger.debug(f"插件已添加到侧边栏: {plugin_name}")
        except Exception as e:
            logger.error(f"添加插件到侧边栏失败 {plugin_name}: {e}")

    def _load_plugins(self):
        """加载插件到侧边栏"""
        try:
            # 获取激活的插件
            active_plugins = plugin_manager.get_active_plugins()

            logger.debug(f"尝试加载插件到侧边栏: {list(active_plugins.keys())}")

            for plugin_name, plugin in active_plugins.items():
                self._add_plugin_to_sidebar(plugin_name, plugin)

            logger.debug(f"加载插件到侧边栏完成: {list(active_plugins.keys())}")

        except Exception as e:
            logger.error(f"加载插件到侧边栏失败: {e}")
    
    def _add_plugin_to_sidebar(self, plugin_name: str, plugin):
        """添加插件到侧边栏"""
        try:
            # 获取插件信息
            plugin_info = plugin.get_info()
            logger.debug(f"正在添加插件到侧边栏: {plugin_name}, 信息: {plugin_info.name}")

            # 添加图标按钮
            icon_path = plugin.get_icon() or ""
            tooltip = plugin_info.description or plugin_info.name
            logger.debug(f"添加图标按钮: {plugin_name}, 图标: {icon_path}, 提示: {tooltip}")
            self._icon_bar.add_icon(plugin_name, icon_path, tooltip)

            # 获取插件UI组件
            plugin_widget = plugin.get_widget()
            if plugin_widget:
                self._content_area.add_content(plugin_name, plugin_widget)
                logger.debug(f"插件UI组件已添加: {plugin_name}")
            else:
                logger.debug(f"插件没有UI组件: {plugin_name}")

            logger.info(f"插件成功添加到侧边栏: {plugin_name}")
            
        except Exception as e:
            logger.error(f"添加插件到侧边栏失败 {plugin_name}: {e}")
    
    def _on_icon_clicked(self, icon_name: str):
        """处理图标点击事件"""
        try:
            current_icon = self._icon_bar.get_current_icon()
            
            if current_icon == icon_name:
                # 点击当前图标，折叠侧边栏
                self.collapse()
            else:
                # 点击其他图标，展开并切换内容
                if self._collapsed:
                    self.expand()
                
                self._content_area.show_content(icon_name)
                self.content_changed.emit(icon_name)
            
        except Exception as e:
            logger.error(f"处理图标点击失败: {e}")
    
    def collapse(self):
        """折叠侧边栏"""
        if self._collapsed:
            return
        
        try:
            self._collapsed = True
            
            # 隐藏内容区域
            self._content_area.hide()
            
            # 清除图标选择
            self._icon_bar.clear_selection()
            
            # 清除内容
            self._content_area.clear_content()
            
            # 发送信号
            self.collapsed_state_changed.emit(True)
            
            # 保存状态
            config_manager.set("app.sidebar.collapsed", True, save=False)
            
            logger.debug("侧边栏已折叠")
            
        except Exception as e:
            logger.error(f"折叠侧边栏失败: {e}")
    
    def expand(self):
        """展开侧边栏"""
        if not self._collapsed:
            return
        
        try:
            self._collapsed = False
            
            # 显示内容区域
            self._content_area.show()
            
            # 发送信号
            self.collapsed_state_changed.emit(False)
            
            # 保存状态
            config_manager.set("app.sidebar.collapsed", False, save=False)
            
            logger.debug("侧边栏已展开")
            
        except Exception as e:
            logger.error(f"展开侧边栏失败: {e}")
    
    def toggle(self):
        """切换侧边栏状态"""
        if self._collapsed:
            self.expand()
        else:
            self.collapse()
    
    def is_collapsed(self) -> bool:
        """检查是否折叠"""
        return self._collapsed
    
    def refresh_plugins(self):
        """刷新插件显示"""
        try:
            # 清除现有插件
            for plugin_name in list(self._content_area._content_widgets.keys()):
                if plugin_name != "default":
                    self._icon_bar.remove_icon(plugin_name)
                    self._content_area.remove_content(plugin_name)
            
            # 重新加载插件
            self._load_plugins()
            
            logger.debug("插件显示已刷新")
            
        except Exception as e:
            logger.error(f"刷新插件显示失败: {e}")
    
    def set_active_plugin(self, plugin_name: str):
        """设置激活的插件"""
        try:
            if not self._collapsed:
                self._icon_bar.set_active_icon(plugin_name)
                self._content_area.show_content(plugin_name)
                self.content_changed.emit(plugin_name)
            
        except Exception as e:
            logger.error(f"设置激活插件失败: {e}")
    
    def get_current_plugin(self) -> Optional[str]:
        """获取当前激活的插件"""
        if self._collapsed:
            return None
        return self._icon_bar.get_current_icon()
    
    def get_icon_bar(self) -> IconBar:
        """获取图标栏"""
        return self._icon_bar
    
    def get_content_area(self) -> ContentArea:
        """获取内容区域"""
        return self._content_area
