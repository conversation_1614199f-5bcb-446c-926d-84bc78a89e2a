"""
主应用程序模块
管理应用程序的生命周期和核心功能
"""

import sys
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, Signal, QObject
from PySide6.QtGui import QIcon

from .config_manager import config_manager
from .plugin_manager import plugin_manager
from .event_bus import event_bus
from .exception_handler import exception_handler
from ..utils.logger import logger


class Application(QObject):
    """主应用程序类"""
    
    # 应用程序信号
    startup_complete = Signal()
    shutdown_started = Signal()
    
    def __init__(self):
        super().__init__()
        self._qt_app: Optional[QApplication] = None
        self._main_window = None
        self._initialized = False
        
        # 应用程序信息
        self.app_name = "MyApp"
        self.app_version = "1.0.0"
        self.app_dir = Path(__file__).parent.parent.parent
        
    def initialize(self) -> bool:
        """初始化应用程序"""
        try:
            if self._initialized:
                logger.warning("应用程序已初始化")
                return True
            
            logger.info("开始初始化应用程序...")
            
            # 1. 创建Qt应用程序
            if not self._create_qt_application():
                return False
            
            # 2. 安装异常处理器
            exception_handler.install()
            
            # 3. 加载配置
            self._load_configurations()
            
            # 4. 设置应用程序属性
            self._setup_application_properties()
            
            # 5. 加载样式
            self._load_styles()
            
            # 6. 初始化插件系统
            self._initialize_plugins()
            
            # 7. 创建主窗口
            self._create_main_window()
            
            self._initialized = True
            logger.info("应用程序初始化完成")
            
            # 延迟发送启动完成信号
            QTimer.singleShot(100, self.startup_complete.emit)
            
            return True
            
        except Exception as e:
            logger.exception(f"应用程序初始化失败: {e}")
            return False
    
    def _create_qt_application(self) -> bool:
        """创建Qt应用程序"""
        try:
            if QApplication.instance() is None:
                self._qt_app = QApplication(sys.argv)
                logger.info("Qt应用程序创建成功")
            else:
                self._qt_app = QApplication.instance()
                logger.info("使用现有Qt应用程序实例")
            
            return True
            
        except Exception as e:
            logger.error(f"创建Qt应用程序失败: {e}")
            return False
    
    def _load_configurations(self):
        """加载配置"""
        try:
            # 配置已在config_manager初始化时自动加载
            self.app_name = config_manager.get("app.name", "MyApp")
            self.app_version = config_manager.get("app.version", "1.0.0")
            
            logger.info(f"配置加载完成: {self.app_name} v{self.app_version}")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
    
    def _setup_application_properties(self):
        """设置应用程序属性"""
        try:
            if self._qt_app:
                self._qt_app.setApplicationName(self.app_name)
                self._qt_app.setApplicationVersion(self.app_version)
                self._qt_app.setOrganizationName("MyApp")
                
                # 设置应用程序图标
                icon_path = self.app_dir / "resources" / "icons" / "app.ico"
                if icon_path.exists():
                    self._qt_app.setWindowIcon(QIcon(str(icon_path)))
                
                logger.debug("应用程序属性设置完成")
                
        except Exception as e:
            logger.error(f"设置应用程序属性失败: {e}")
    
    def _load_styles(self):
        """加载样式表"""
        try:
            style_path = self.app_dir / "resources" / "styles" / "main.qss"
            if style_path.exists():
                with open(style_path, 'r', encoding='utf-8') as f:
                    style_sheet = f.read()
                    if self._qt_app:
                        self._qt_app.setStyleSheet(style_sheet)
                        logger.info("样式表加载成功")
            else:
                logger.warning(f"样式表文件不存在: {style_path}")
                
        except Exception as e:
            logger.error(f"加载样式表失败: {e}")
    
    def _initialize_plugins(self):
        """初始化插件系统"""
        try:
            # 发现插件
            available_plugins = plugin_manager.discover_plugins()
            logger.info(f"发现插件: {available_plugins}")
            
            # 加载启用的插件
            plugin_manager.load_enabled_plugins()
            
            logger.info("插件系统初始化完成")
            
        except Exception as e:
            logger.error(f"初始化插件系统失败: {e}")
    
    def _create_main_window(self):
        """创建主窗口"""
        try:
            # 延迟导入避免循环依赖
            from ..ui.main_window import MainWindow
            
            self._main_window = MainWindow()
            
            # 设置窗口属性
            window_config = config_manager.get("app.window", {})
            if window_config:
                width = window_config.get("width", 1200)
                height = window_config.get("height", 800)
                min_width = window_config.get("min_width", 800)
                min_height = window_config.get("min_height", 600)
                title = window_config.get("title", self.app_name)
                
                self._main_window.resize(width, height)
                self._main_window.setMinimumSize(min_width, min_height)
                self._main_window.setWindowTitle(title)
            
            logger.info("主窗口创建成功")
            
        except Exception as e:
            logger.error(f"创建主窗口失败: {e}")
    
    def run(self) -> int:
        """运行应用程序"""
        try:
            if not self._initialized:
                if not self.initialize():
                    return 1
            
            if self._main_window:
                self._main_window.show()
                logger.info("应用程序启动成功")
            
            if self._qt_app:
                return self._qt_app.exec()
            else:
                logger.error("Qt应用程序未创建")
                return 1
                
        except Exception as e:
            logger.exception(f"运行应用程序失败: {e}")
            return 1
    
    def shutdown(self):
        """关闭应用程序"""
        try:
            logger.info("开始关闭应用程序...")
            self.shutdown_started.emit()
            
            # 停用所有插件
            active_plugins = plugin_manager.get_active_plugins()
            for plugin_name in active_plugins:
                plugin_manager.deactivate_plugin(plugin_name)
            
            # 保存配置
            config_manager.save_config("app")
            config_manager.save_config("plugins")
            
            # 清理事件监听器
            event_bus.clear_listeners()
            
            # 卸载异常处理器
            exception_handler.uninstall()
            
            # 关闭主窗口
            if self._main_window:
                self._main_window.close()
            
            # 退出Qt应用程序
            if self._qt_app:
                self._qt_app.quit()
            
            logger.info("应用程序关闭完成")
            
        except Exception as e:
            logger.exception(f"关闭应用程序失败: {e}")
    
    def get_main_window(self):
        """获取主窗口"""
        return self._main_window
    
    def get_qt_app(self) -> Optional[QApplication]:
        """获取Qt应用程序实例"""
        return self._qt_app
    
    def restart(self):
        """重启应用程序"""
        try:
            logger.info("重启应用程序...")
            
            # 关闭当前应用程序
            self.shutdown()
            
            # 重新启动
            if self._qt_app:
                self._qt_app.quit()
                # 这里可以添加重启逻辑，比如调用外部脚本
                
        except Exception as e:
            logger.exception(f"重启应用程序失败: {e}")


# 全局应用程序实例
app = Application()
