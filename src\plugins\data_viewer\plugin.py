"""
数据视图插件
提供数据查看和分析功能
"""

from typing import Optional, List, Dict, Any
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                               QTableWidgetItem, QPushButton, QLabel, QLineEdit,
                               QFileDialog, QMessageBox, QSplitter, QTextEdit,
                               QComboBox, QSpinBox, QCheckBox, QTabWidget)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QFont

from ..base_plugin import BasePlugin
from ...core.plugin_manager import PluginInfo
from ...utils.logger import logger
import json
import csv
import os


class DataTableWidget(QTableWidget):
    """数据表格组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSortingEnabled(True)
        
        # 设置表格样式
        self.horizontalHeader().setStretchLastSection(True)
        self.verticalHeader().setVisible(False)
    
    def load_data(self, data: List[Dict[str, Any]], headers: List[str] = None):
        """加载数据到表格"""
        try:
            if not data:
                self.clear()
                return
            
            # 设置表格尺寸
            row_count = len(data)
            if headers:
                col_count = len(headers)
                column_names = headers
            else:
                # 从第一行数据获取列名
                column_names = list(data[0].keys()) if data else []
                col_count = len(column_names)
            
            self.setRowCount(row_count)
            self.setColumnCount(col_count)
            self.setHorizontalHeaderLabels(column_names)
            
            # 填充数据
            for row_idx, row_data in enumerate(data):
                for col_idx, col_name in enumerate(column_names):
                    value = row_data.get(col_name, "")
                    item = QTableWidgetItem(str(value))
                    self.setItem(row_idx, col_idx, item)
            
            # 调整列宽
            self.resizeColumnsToContents()
            
            logger.debug(f"数据加载完成: {row_count}行 x {col_count}列")
            
        except Exception as e:
            logger.error(f"加载数据到表格失败: {e}")
    
    def get_selected_data(self) -> List[Dict[str, Any]]:
        """获取选中的数据"""
        try:
            selected_rows = set()
            for item in self.selectedItems():
                selected_rows.add(item.row())
            
            if not selected_rows:
                return []
            
            # 获取列名
            headers = []
            for col in range(self.columnCount()):
                headers.append(self.horizontalHeaderItem(col).text())
            
            # 获取选中行的数据
            result = []
            for row in sorted(selected_rows):
                row_data = {}
                for col, header in enumerate(headers):
                    item = self.item(row, col)
                    row_data[header] = item.text() if item else ""
                result.append(row_data)
            
            return result
            
        except Exception as e:
            logger.error(f"获取选中数据失败: {e}")
            return []


class DataStatsWidget(QWidget):
    """数据统计组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 统计信息显示
        self._stats_text = QTextEdit()
        self._stats_text.setReadOnly(True)
        self._stats_text.setMaximumHeight(200)
        layout.addWidget(self._stats_text)
    
    def update_stats(self, data: List[Dict[str, Any]]):
        """更新统计信息"""
        try:
            if not data:
                self._stats_text.setText("无数据")
                return
            
            stats_info = []
            stats_info.append(f"总行数: {len(data)}")
            
            if data:
                stats_info.append(f"总列数: {len(data[0])}")
                
                # 分析每列的数据类型和统计信息
                for col_name in data[0].keys():
                    values = [row.get(col_name, "") for row in data]
                    non_empty_values = [v for v in values if str(v).strip()]
                    
                    stats_info.append(f"\n列: {col_name}")
                    stats_info.append(f"  非空值: {len(non_empty_values)}")
                    stats_info.append(f"  空值: {len(values) - len(non_empty_values)}")
                    
                    # 尝试分析数值类型
                    numeric_values = []
                    for v in non_empty_values:
                        try:
                            numeric_values.append(float(str(v)))
                        except:
                            pass
                    
                    if numeric_values:
                        stats_info.append(f"  数值个数: {len(numeric_values)}")
                        stats_info.append(f"  最小值: {min(numeric_values):.2f}")
                        stats_info.append(f"  最大值: {max(numeric_values):.2f}")
                        stats_info.append(f"  平均值: {sum(numeric_values)/len(numeric_values):.2f}")
            
            self._stats_text.setText("\n".join(stats_info))
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
            self._stats_text.setText(f"统计失败: {str(e)}")


class DataViewerWidget(QWidget):
    """数据视图主组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._current_data: List[Dict[str, Any]] = []
        self._data_table: DataTableWidget = None
        self._stats_widget: DataStatsWidget = None
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self._load_btn = QPushButton("加载数据")
        self._load_btn.clicked.connect(self._load_data)
        toolbar_layout.addWidget(self._load_btn)
        
        self._export_btn = QPushButton("导出数据")
        self._export_btn.clicked.connect(self._export_data)
        self._export_btn.setEnabled(False)
        toolbar_layout.addWidget(self._export_btn)
        
        self._clear_btn = QPushButton("清除数据")
        self._clear_btn.clicked.connect(self._clear_data)
        self._clear_btn.setEnabled(False)
        toolbar_layout.addWidget(self._clear_btn)
        
        toolbar_layout.addStretch()
        
        # 行数限制
        toolbar_layout.addWidget(QLabel("显示行数:"))
        self._row_limit_spin = QSpinBox()
        self._row_limit_spin.setRange(10, 10000)
        self._row_limit_spin.setValue(1000)
        self._row_limit_spin.setSuffix(" 行")
        toolbar_layout.addWidget(self._row_limit_spin)
        
        layout.addLayout(toolbar_layout)
        
        # 主要内容区域
        tab_widget = QTabWidget()
        
        # 数据表格标签页
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        
        self._data_table = DataTableWidget()
        table_layout.addWidget(self._data_table)
        
        tab_widget.addTab(table_widget, "数据表格")
        
        # 统计信息标签页
        self._stats_widget = DataStatsWidget()
        tab_widget.addTab(self._stats_widget, "统计信息")
        
        layout.addWidget(tab_widget)
    
    def _connect_signals(self):
        """连接信号"""
        pass
    
    def _load_data(self):
        """加载数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择数据文件", os.path.expanduser("~"),
                "数据文件 (*.csv *.json *.txt);;CSV文件 (*.csv);;JSON文件 (*.json);;所有文件 (*.*)"
            )
            
            if not file_path:
                return
            
            # 根据文件扩展名选择加载方式
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                data = self._load_csv_file(file_path)
            elif file_ext == '.json':
                data = self._load_json_file(file_path)
            else:
                # 尝试作为CSV加载
                data = self._load_csv_file(file_path)
            
            if data:
                # 限制显示行数
                row_limit = self._row_limit_spin.value()
                if len(data) > row_limit:
                    data = data[:row_limit]
                    QMessageBox.information(
                        self, "提示", 
                        f"数据行数超过限制，仅显示前 {row_limit} 行"
                    )
                
                self._current_data = data
                self._data_table.load_data(data)
                self._stats_widget.update_stats(data)
                
                self._export_btn.setEnabled(True)
                self._clear_btn.setEnabled(True)
                
                logger.info(f"数据加载成功: {file_path}, {len(data)} 行")
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            QMessageBox.warning(self, "错误", f"加载数据失败: {str(e)}")
    
    def _load_csv_file(self, file_path: str) -> List[Dict[str, Any]]:
        """加载CSV文件"""
        try:
            data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(dict(row))
            return data
        except UnicodeDecodeError:
            # 尝试GBK编码
            with open(file_path, 'r', encoding='gbk') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(dict(row))
            return data
    
    def _load_json_file(self, file_path: str) -> List[Dict[str, Any]]:
        """加载JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # 如果是单个对象，转换为列表
        if isinstance(data, dict):
            return [data]
        elif isinstance(data, list):
            return data
        else:
            raise ValueError("不支持的JSON格式")
    
    def _export_data(self):
        """导出数据"""
        try:
            if not self._current_data:
                QMessageBox.warning(self, "警告", "没有数据可导出")
                return
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出数据", os.path.expanduser("~"),
                "CSV文件 (*.csv);;JSON文件 (*.json)"
            )
            
            if not file_path:
                return
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self._current_data, f, ensure_ascii=False, indent=2)
            else:
                # 默认导出为CSV
                if not file_path.endswith('.csv'):
                    file_path += '.csv'
                
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    if self._current_data:
                        writer = csv.DictWriter(f, fieldnames=self._current_data[0].keys())
                        writer.writeheader()
                        writer.writerows(self._current_data)
            
            QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            logger.info(f"数据导出成功: {file_path}")
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            QMessageBox.warning(self, "错误", f"导出数据失败: {str(e)}")
    
    def _clear_data(self):
        """清除数据"""
        self._current_data = []
        self._data_table.clear()
        self._stats_widget.update_stats([])
        
        self._export_btn.setEnabled(False)
        self._clear_btn.setEnabled(False)
        
        logger.info("数据已清除")


class DataViewerPlugin(BasePlugin):
    """数据视图插件"""
    
    def __init__(self):
        super().__init__()
        self._widget: Optional[DataViewerWidget] = None
    
    def get_info(self) -> PluginInfo:
        """获取插件信息"""
        return PluginInfo(
            name="数据视图",
            version="1.0.0",
            description="查看和分析数据文件",
            author="MyApp Team",
            icon="resources/icons/data.svg",
            dependencies=[]
        )
    
    def activate(self) -> bool:
        """激活插件"""
        try:
            self.log_info("正在激活数据视图插件...")
            
            # 创建UI组件
            if self._widget is None:
                self._widget = DataViewerWidget()
            
            # 订阅相关事件
            self.subscribe_event("data_file_opened", self._on_data_file_opened)
            
            self._set_active(True)
            self.on_activate()
            
            self.log_info("数据视图插件激活成功")
            return True
            
        except Exception as e:
            self._handle_error(f"激活插件失败: {e}")
            return False
    
    def deactivate(self) -> bool:
        """停用插件"""
        try:
            self.log_info("正在停用数据视图插件...")
            
            # 取消事件订阅
            self.unsubscribe_event("data_file_opened", self._on_data_file_opened)
            
            self._set_active(False)
            self.on_deactivate()
            
            self.log_info("数据视图插件停用成功")
            return True
            
        except Exception as e:
            self._handle_error(f"停用插件失败: {e}")
            return False
    
    def create_sidebar_widget(self) -> Optional[QWidget]:
        """创建侧边栏组件"""
        if self._widget is None:
            self._widget = DataViewerWidget()
        return self._widget
    
    def _on_data_file_opened(self, event):
        """处理数据文件打开事件"""
        if hasattr(event, 'data') and event.data:
            file_path = event.data
            self.log_info(f"收到数据文件打开事件: {file_path}")


# 插件实例
plugin_instance = DataViewerPlugin()
