"""
项目管理插件
提供项目文件和结构管理功能
"""

from typing import Optional
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, 
                               QTreeWidgetItem, QPushButton, QLabel, QLineEdit,
                               QFileDialog, QMessageBox, QSplitter, QTextEdit)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QFont

from ..base_plugin import BasePlugin
from ...core.plugin_manager import PluginInfo
from ...utils.logger import logger
import os
from pathlib import Path


class ProjectTreeWidget(QTreeWidget):
    """项目树组件"""
    
    # 信号
    file_selected = Signal(str)  # 文件路径
    folder_selected = Signal(str)  # 文件夹路径
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setHeaderLabel("项目结构")
        self.setRootIsDecorated(True)
        self.setAlternatingRowColors(True)
        
        # 连接信号
        self.itemClicked.connect(self._on_item_clicked)
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
    
    def load_project(self, project_path: str):
        """加载项目"""
        try:
            self.clear()
            
            if not os.path.exists(project_path):
                return
            
            project_name = os.path.basename(project_path)
            root_item = QTreeWidgetItem(self, [project_name])
            root_item.setData(0, Qt.ItemDataRole.UserRole, project_path)
            
            # 递归加载目录结构
            self._load_directory(root_item, project_path)
            
            # 展开根节点
            root_item.setExpanded(True)
            
            logger.debug(f"项目加载完成: {project_path}")
            
        except Exception as e:
            logger.error(f"加载项目失败: {e}")
    
    def _load_directory(self, parent_item: QTreeWidgetItem, dir_path: str):
        """递归加载目录"""
        try:
            for item_name in sorted(os.listdir(dir_path)):
                if item_name.startswith('.'):
                    continue
                
                item_path = os.path.join(dir_path, item_name)
                tree_item = QTreeWidgetItem(parent_item, [item_name])
                tree_item.setData(0, Qt.ItemDataRole.UserRole, item_path)
                
                if os.path.isdir(item_path):
                    # 文件夹图标
                    tree_item.setIcon(0, self.style().standardIcon(
                        self.style().StandardPixmap.SP_DirIcon))
                    
                    # 递归加载子目录（限制深度避免性能问题）
                    if parent_item.parent() is None or parent_item.parent().parent() is None:
                        self._load_directory(tree_item, item_path)
                else:
                    # 文件图标
                    tree_item.setIcon(0, self.style().standardIcon(
                        self.style().StandardPixmap.SP_FileIcon))
                        
        except Exception as e:
            logger.error(f"加载目录失败 {dir_path}: {e}")
    
    def _on_item_clicked(self, item: QTreeWidgetItem, column: int):
        """处理项目点击事件"""
        item_path = item.data(0, Qt.ItemDataRole.UserRole)
        if item_path:
            if os.path.isfile(item_path):
                self.file_selected.emit(item_path)
            else:
                self.folder_selected.emit(item_path)
    
    def _on_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """处理项目双击事件"""
        item_path = item.data(0, Qt.ItemDataRole.UserRole)
        if item_path and os.path.isfile(item_path):
            # 双击文件时可以打开文件
            self.file_selected.emit(item_path)


class FilePreviewWidget(QWidget):
    """文件预览组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._current_file = ""
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 文件信息标签
        self._file_label = QLabel("未选择文件")
        self._file_label.setStyleSheet("font-weight: bold; padding: 5px;")
        layout.addWidget(self._file_label)
        
        # 文件内容显示
        self._content_text = QTextEdit()
        self._content_text.setReadOnly(True)
        self._content_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self._content_text)
    
    def preview_file(self, file_path: str):
        """预览文件"""
        try:
            self._current_file = file_path
            file_name = os.path.basename(file_path)
            self._file_label.setText(f"文件: {file_name}")
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > 1024 * 1024:  # 1MB
                self._content_text.setText("文件过大，无法预览")
                return
            
            # 尝试读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self._content_text.setText(content)
            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                        self._content_text.setText(content)
                except:
                    self._content_text.setText("无法解码文件内容（可能是二进制文件）")
            
        except Exception as e:
            logger.error(f"预览文件失败: {e}")
            self._content_text.setText(f"预览失败: {str(e)}")
    
    def clear_preview(self):
        """清除预览"""
        self._current_file = ""
        self._file_label.setText("未选择文件")
        self._content_text.clear()


class ProjectManagerWidget(QWidget):
    """项目管理主组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._current_project = ""
        self._project_tree: ProjectTreeWidget = None
        self._file_preview: FilePreviewWidget = None
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self._open_btn = QPushButton("打开项目")
        self._open_btn.clicked.connect(self._open_project)
        toolbar_layout.addWidget(self._open_btn)
        
        self._refresh_btn = QPushButton("刷新")
        self._refresh_btn.clicked.connect(self._refresh_project)
        self._refresh_btn.setEnabled(False)
        toolbar_layout.addWidget(self._refresh_btn)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 项目树
        self._project_tree = ProjectTreeWidget()
        splitter.addWidget(self._project_tree)
        
        # 文件预览
        self._file_preview = FilePreviewWidget()
        splitter.addWidget(self._file_preview)
        
        # 设置分割比例
        splitter.setSizes([300, 400])
        
        layout.addWidget(splitter)
    
    def _connect_signals(self):
        """连接信号"""
        if self._project_tree:
            self._project_tree.file_selected.connect(self._on_file_selected)
            self._project_tree.folder_selected.connect(self._on_folder_selected)
    
    def _open_project(self):
        """打开项目"""
        try:
            project_path = QFileDialog.getExistingDirectory(
                self, "选择项目目录", os.path.expanduser("~")
            )
            
            if project_path:
                self._current_project = project_path
                self._project_tree.load_project(project_path)
                self._refresh_btn.setEnabled(True)
                self._file_preview.clear_preview()
                
                logger.info(f"打开项目: {project_path}")
                
        except Exception as e:
            logger.error(f"打开项目失败: {e}")
            QMessageBox.warning(self, "错误", f"打开项目失败: {str(e)}")
    
    def _refresh_project(self):
        """刷新项目"""
        if self._current_project:
            self._project_tree.load_project(self._current_project)
            self._file_preview.clear_preview()
    
    def _on_file_selected(self, file_path: str):
        """处理文件选择事件"""
        self._file_preview.preview_file(file_path)
    
    def _on_folder_selected(self, folder_path: str):
        """处理文件夹选择事件"""
        self._file_preview.clear_preview()


class ProjectManagerPlugin(BasePlugin):
    """项目管理插件"""
    
    def __init__(self):
        super().__init__()
        self._widget: Optional[ProjectManagerWidget] = None
    
    def get_info(self) -> PluginInfo:
        """获取插件信息"""
        return PluginInfo(
            name="项目管理",
            version="1.0.0",
            description="管理项目文件和结构",
            author="MyApp Team",
            icon="resources/icons/project.svg",
            dependencies=[]
        )
    
    def activate(self) -> bool:
        """激活插件"""
        try:
            self.log_info("正在激活项目管理插件...")
            
            # 创建UI组件
            if self._widget is None:
                self._widget = ProjectManagerWidget()
            
            # 订阅相关事件
            self.subscribe_event("project_opened", self._on_project_opened)
            self.subscribe_event("project_closed", self._on_project_closed)
            
            self._set_active(True)
            self.on_activate()
            
            self.log_info("项目管理插件激活成功")
            return True
            
        except Exception as e:
            self._handle_error(f"激活插件失败: {e}")
            return False
    
    def deactivate(self) -> bool:
        """停用插件"""
        try:
            self.log_info("正在停用项目管理插件...")
            
            # 取消事件订阅
            self.unsubscribe_event("project_opened", self._on_project_opened)
            self.unsubscribe_event("project_closed", self._on_project_closed)
            
            self._set_active(False)
            self.on_deactivate()
            
            self.log_info("项目管理插件停用成功")
            return True
            
        except Exception as e:
            self._handle_error(f"停用插件失败: {e}")
            return False
    
    def create_sidebar_widget(self) -> Optional[QWidget]:
        """创建侧边栏组件"""
        if self._widget is None:
            self._widget = ProjectManagerWidget()
        return self._widget
    
    def create_workspace_widget(self) -> Optional[QWidget]:
        """创建工作区组件"""
        # 项目管理插件主要在侧边栏显示，工作区可以显示详细信息
        return None
    
    def _on_project_opened(self, event):
        """处理项目打开事件"""
        if hasattr(event, 'data') and event.data:
            project_path = event.data
            self.log_info(f"收到项目打开事件: {project_path}")
    
    def _on_project_closed(self, event):
        """处理项目关闭事件"""
        self.log_info("收到项目关闭事件")
    
    def on_activate(self):
        """激活回调"""
        self.emit_event("plugin_ui_ready", {
            "plugin": "project_manager",
            "widget": self._widget
        })
    
    def on_deactivate(self):
        """停用回调"""
        self.emit_event("plugin_ui_cleanup", {
            "plugin": "project_manager"
        })


# 插件实例（插件管理器会查找这个实例）
plugin_instance = ProjectManagerPlugin()
