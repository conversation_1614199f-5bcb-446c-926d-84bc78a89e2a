"""
PySide6 高效软件框架 - 主程序入口
"""
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.application import app
from core.config_manager import config
from core.event_system import event_system
from core.exception_handler import global_exception_handler
from ui.main_window import MainWindow
from ui.layout_manager import LayoutManager
from ui.sidebar import Sidebar
from ui.workspace import Workspace
from plugins.plugin_manager import plugin_manager


class FrameworkApp:
    """框架应用程序主类"""

    def __init__(self):
        self.main_window = None
        self.layout_manager = None
        self.sidebar = None
        self.workspace = None

        # 设置事件处理
        self._setup_event_handlers()

    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 订阅插件事件
        event_system.subscribe('sidebar_panel_registered', self._on_sidebar_panel_registered)
        event_system.subscribe('plugin_message', self._on_plugin_message)
        event_system.subscribe('file_open_requested', self._on_file_open_requested)

        # 订阅异常事件
        event_system.subscribe('ui_exception', self._on_ui_exception)

    def initialize(self):
        """初始化应用程序"""
        try:
            # 初始化Qt应用程序
            print("初始化Qt应用程序...")
            app.initialize()
            print("Qt应用程序初始化完成")

            # 创建主窗口
            print("创建主窗口...")
            self.main_window = MainWindow()
            app.set_main_window(self.main_window)
            print("主窗口创建完成")

            # 创建布局管理器
            print("创建布局管理器...")
            self.layout_manager = LayoutManager(self.main_window)
            print("布局管理器创建完成")

            # 创建侧边栏
            print("创建侧边栏...")
            self.sidebar = Sidebar()
            self.main_window.set_sidebar(self.sidebar)
            print("侧边栏创建完成")

            # 创建工作区
            print("创建工作区...")
            self.workspace = Workspace()
            self.main_window.set_workspace(self.workspace)
            print("工作区创建完成")

            # 注册组件到布局管理器
            print("注册组件...")
            # 暂时跳过组件注册，直接使用已设置的组件
            # self.layout_manager.register_component("sidebar", self.sidebar, "sidebar")
            # self.layout_manager.register_component("workspace", self.workspace, "workspace")
            print("组件注册完成")

            # 恢复窗口状态
            print("恢复窗口状态...")
            self.main_window.restore_window_state()
            print("窗口状态恢复完成")

            print("框架初始化完成")
        except Exception as e:
            print(f"初始化过程中出错: {e}")
            import traceback
            traceback.print_exc()
            raise

    def run(self):
        """运行应用程序"""
        return app.run()

    def _on_sidebar_panel_registered(self, event):
        """侧边栏面板注册事件处理"""
        data = event.data
        self.sidebar.add_panel(
            data['panel_id'],
            data['widget'],
            data.get('icon'),
            data.get('title', data['panel_id']),
            data.get('title', data['panel_id'])
        )

    def _on_plugin_message(self, event):
        """插件消息事件处理"""
        data = event.data
        message = data['message']
        level = data.get('level', 'info')
        plugin_id = data.get('plugin_id', '')

        # 在状态栏显示消息
        if self.main_window:
            timeout = 3000 if level == 'info' else 5000
            self.main_window.show_status_message(f"[{plugin_id}] {message}", timeout)

    def _on_file_open_requested(self, event):
        """文件打开请求事件处理"""
        data = event.data
        file_path = data.get('file_path')

        if file_path and Path(file_path).exists():
            # 创建简单的文本编辑器标签页
            self._open_text_file(file_path)

    def _open_text_file(self, file_path: str):
        """打开文本文件"""
        from PySide6.QtWidgets import QTextEdit, QVBoxLayout, QWidget, QLabel

        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 创建编辑器组件
            editor_widget = QWidget()
            layout = QVBoxLayout(editor_widget)

            # 文件路径标签
            path_label = QLabel(file_path)
            path_label.setStyleSheet("color: #666666; font-size: 10px; padding: 2px;")
            layout.addWidget(path_label)

            # 文本编辑器
            text_edit = QTextEdit()
            text_edit.setPlainText(content)
            layout.addWidget(text_edit)

            # 添加到工作区
            file_name = Path(file_path).name
            tab_id = f"file_{hash(file_path)}"

            self.workspace.add_tab(
                tab_id,
                editor_widget,
                file_name,
                tooltip=file_path
            )

        except Exception as e:
            if self.main_window:
                self.main_window.show_status_message(f"打开文件失败: {e}", 5000)

    def _on_ui_exception(self, event):
        """UI异常事件处理"""
        exception_info = event.data

        # 在状态栏显示错误信息
        if self.main_window:
            self.main_window.show_status_message(
                f"错误: {exception_info.message}", 5000
            )


def main():
    """主函数"""
    try:
        print("开始启动应用程序...")

        # 创建框架应用程序
        print("创建框架应用程序...")
        framework_app = FrameworkApp()

        # 初始化
        print("初始化应用程序...")
        framework_app.initialize()

        # 运行应用程序
        print("运行应用程序...")
        exit_code = framework_app.run()

        print(f"应用程序退出，退出码: {exit_code}")
        return exit_code

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())