"""
程序入口文件
启动MyApp应用程序
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.application import app
from src.utils.logger import logger


def main():
    """主函数"""
    try:
        logger.info("=" * 50)
        logger.info("MyApp 启动中...")
        logger.info("=" * 50)
        
        # 初始化并运行应用程序
        exit_code = app.run()
        
        logger.info("=" * 50)
        logger.info("MyApp 已退出")
        logger.info("=" * 50)
        
        return exit_code
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        return 0
    except Exception as e:
        logger.exception(f"程序运行失败: {e}")
        return 1
    finally:
        # 确保应用程序正确关闭
        try:
            app.shutdown()
        except:
            pass


if __name__ == "__main__":
    sys.exit(main())
