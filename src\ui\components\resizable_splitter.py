"""
可调整分割器组件
支持拖拽调整和自动折叠功能的分割器
"""

from typing import List, Callable, Optional
from PySide6.QtWidgets import QSplitter, QWidget, QSizePolicy
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QTimer
from PySide6.QtGui import QCursor

from ...core.config_manager import config_manager
from ...utils.logger import logger


class ResizableSplitter(QSplitter):
    """可调整分割器"""
    
    # 自定义信号
    panel_collapsed = Signal(int)  # 面板索引
    panel_expanded = Signal(int)   # 面板索引
    sizes_changed = Signal(list)   # 尺寸列表
    
    def __init__(self, orientation=Qt.Orientation.Horizontal, parent=None):
        super().__init__(orientation, parent)
        
        self._min_sizes: List[int] = []
        self._collapse_thresholds: List[int] = []
        self._collapsed_states: List[bool] = []
        self._original_sizes: List[int] = []
        self._collapse_callbacks: List[Optional[Callable]] = []
        self._expand_callbacks: List[Optional[Callable]] = []
        
        # 动画相关
        self._animation: Optional[QPropertyAnimation] = None
        self._animation_target_sizes: List[int] = []
        
        # 设置基本属性
        self.setChildrenCollapsible(False)  # 禁用默认折叠
        self.setHandleWidth(2)
        
        # 连接信号
        self.splitterMoved.connect(self._on_splitter_moved)
        
        # 延迟处理定时器
        self._resize_timer = QTimer()
        self._resize_timer.setSingleShot(True)
        self._resize_timer.timeout.connect(self._handle_resize_delayed)
        
        logger.debug("创建可调整分割器")
    
    def addWidget(self, widget: QWidget):
        """添加组件"""
        super().addWidget(widget)
        
        # 初始化新组件的状态
        index = self.count() - 1
        self._min_sizes.append(100)  # 默认最小尺寸
        self._collapse_thresholds.append(50)  # 默认折叠阈值
        self._collapsed_states.append(False)
        self._original_sizes.append(200)  # 默认原始尺寸
        self._collapse_callbacks.append(None)
        self._expand_callbacks.append(None)
        
        logger.debug(f"添加组件到分割器，索引: {index}")
    
    def insertWidget(self, index: int, widget: QWidget):
        """插入组件"""
        super().insertWidget(index, widget)
        
        # 在指定位置插入状态
        self._min_sizes.insert(index, 100)
        self._collapse_thresholds.insert(index, 50)
        self._collapsed_states.insert(index, False)
        self._original_sizes.insert(index, 200)
        self._collapse_callbacks.insert(index, None)
        self._expand_callbacks.insert(index, None)
        
        logger.debug(f"插入组件到分割器，索引: {index}")
    
    def set_panel_config(self, index: int, min_size: int = 100, 
                        collapse_threshold: int = 50, default_size: int = 200):
        """
        设置面板配置
        
        Args:
            index: 面板索引
            min_size: 最小尺寸
            collapse_threshold: 折叠阈值
            default_size: 默认尺寸
        """
        if 0 <= index < len(self._min_sizes):
            self._min_sizes[index] = min_size
            self._collapse_thresholds[index] = collapse_threshold
            self._original_sizes[index] = default_size
            
            logger.debug(f"设置面板配置 {index}: min={min_size}, threshold={collapse_threshold}, default={default_size}")
    
    def set_collapse_callback(self, index: int, callback: Callable):
        """设置折叠回调函数"""
        if 0 <= index < len(self._collapse_callbacks):
            self._collapse_callbacks[index] = callback
    
    def set_expand_callback(self, index: int, callback: Callable):
        """设置展开回调函数"""
        if 0 <= index < len(self._expand_callbacks):
            self._expand_callbacks[index] = callback
    
    def _on_splitter_moved(self, pos: int, index: int):
        """处理分割器移动事件"""
        # 使用定时器延迟处理，避免频繁触发
        self._resize_timer.start(100)
    
    def _handle_resize_delayed(self):
        """延迟处理尺寸调整"""
        current_sizes = self.sizes()
        
        for i, size in enumerate(current_sizes):
            if i >= len(self._collapsed_states):
                continue
            
            # 检查是否需要折叠
            if not self._collapsed_states[i] and size <= self._collapse_thresholds[i]:
                self._collapse_panel(i, animate=True)
            
            # 检查是否需要展开
            elif self._collapsed_states[i] and size > self._collapse_thresholds[i]:
                self._expand_panel(i, animate=True)
        
        # 发送尺寸变化信号
        self.sizes_changed.emit(current_sizes)
    
    def collapse_panel(self, index: int, animate: bool = True):
        """折叠面板"""
        if 0 <= index < len(self._collapsed_states) and not self._collapsed_states[index]:
            self._collapse_panel(index, animate)
    
    def expand_panel(self, index: int, animate: bool = True):
        """展开面板"""
        if 0 <= index < len(self._collapsed_states) and self._collapsed_states[index]:
            self._expand_panel(index, animate)
    
    def toggle_panel(self, index: int, animate: bool = True):
        """切换面板状态"""
        if 0 <= index < len(self._collapsed_states):
            if self._collapsed_states[index]:
                self.expand_panel(index, animate)
            else:
                self.collapse_panel(index, animate)
    
    def _collapse_panel(self, index: int, animate: bool = True):
        """内部折叠面板方法"""
        if index >= len(self._collapsed_states):
            return
        
        # 保存当前尺寸
        current_sizes = self.sizes()
        if index < len(current_sizes):
            self._original_sizes[index] = max(current_sizes[index], self._min_sizes[index])
        
        # 设置折叠状态
        self._collapsed_states[index] = True
        
        # 计算新尺寸
        new_sizes = current_sizes.copy()
        new_sizes[index] = 0
        
        # 应用新尺寸
        if animate:
            self._animate_to_sizes(new_sizes)
        else:
            self.setSizes(new_sizes)
        
        # 隐藏组件
        widget = self.widget(index)
        if widget:
            widget.hide()
        
        # 调用回调函数
        if index < len(self._collapse_callbacks) and self._collapse_callbacks[index]:
            try:
                self._collapse_callbacks[index]()
            except Exception as e:
                logger.error(f"折叠回调执行失败: {e}")
        
        # 发送信号
        self.panel_collapsed.emit(index)
        
        logger.debug(f"折叠面板: {index}")
    
    def _expand_panel(self, index: int, animate: bool = True):
        """内部展开面板方法"""
        if index >= len(self._collapsed_states):
            return
        
        # 设置展开状态
        self._collapsed_states[index] = False
        
        # 显示组件
        widget = self.widget(index)
        if widget:
            widget.show()
        
        # 计算新尺寸
        current_sizes = self.sizes()
        new_sizes = current_sizes.copy()
        new_sizes[index] = self._original_sizes[index]
        
        # 应用新尺寸
        if animate:
            self._animate_to_sizes(new_sizes)
        else:
            self.setSizes(new_sizes)
        
        # 调用回调函数
        if index < len(self._expand_callbacks) and self._expand_callbacks[index]:
            try:
                self._expand_callbacks[index]()
            except Exception as e:
                logger.error(f"展开回调执行失败: {e}")
        
        # 发送信号
        self.panel_expanded.emit(index)
        
        logger.debug(f"展开面板: {index}")
    
    def _animate_to_sizes(self, target_sizes: List[int]):
        """动画到目标尺寸"""
        if self._animation and self._animation.state() == QPropertyAnimation.State.Running:
            self._animation.stop()
        
        self._animation_target_sizes = target_sizes
        
        # 创建动画
        self._animation = QPropertyAnimation(self, b"sizes")
        self._animation.setDuration(200)
        self._animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self._animation.setStartValue(self.sizes())
        self._animation.setEndValue(target_sizes)
        self._animation.start()
    
    def is_panel_collapsed(self, index: int) -> bool:
        """检查面板是否折叠"""
        if 0 <= index < len(self._collapsed_states):
            return self._collapsed_states[index]
        return False
    
    def get_panel_original_size(self, index: int) -> int:
        """获取面板原始尺寸"""
        if 0 <= index < len(self._original_sizes):
            return self._original_sizes[index]
        return 200
    
    def save_state(self, config_key: str):
        """保存分割器状态到配置"""
        try:
            state = {
                "sizes": self.sizes(),
                "collapsed_states": self._collapsed_states.copy(),
                "original_sizes": self._original_sizes.copy()
            }
            config_manager.set(config_key, state)
            logger.debug(f"保存分割器状态: {config_key}")
        except Exception as e:
            logger.error(f"保存分割器状态失败: {e}")
    
    def restore_state(self, config_key: str):
        """从配置恢复分割器状态"""
        try:
            state = config_manager.get(config_key)
            if state and isinstance(state, dict):
                # 恢复尺寸
                if "sizes" in state:
                    self.setSizes(state["sizes"])
                
                # 恢复折叠状态
                if "collapsed_states" in state:
                    self._collapsed_states = state["collapsed_states"].copy()
                    
                    # 根据折叠状态隐藏/显示组件
                    for i, collapsed in enumerate(self._collapsed_states):
                        widget = self.widget(i)
                        if widget:
                            widget.setVisible(not collapsed)
                
                # 恢复原始尺寸
                if "original_sizes" in state:
                    self._original_sizes = state["original_sizes"].copy()
                
                logger.debug(f"恢复分割器状态: {config_key}")
        except Exception as e:
            logger.error(f"恢复分割器状态失败: {e}")
    
    def get_effective_sizes(self) -> List[int]:
        """获取有效尺寸（考虑折叠状态）"""
        sizes = self.sizes()
        effective_sizes = []
        
        for i, size in enumerate(sizes):
            if i < len(self._collapsed_states) and self._collapsed_states[i]:
                effective_sizes.append(0)
            else:
                effective_sizes.append(size)
        
        return effective_sizes
