"""
辅助函数模块
提供通用的辅助功能
"""

import os
import sys
from pathlib import Path
from typing import Optional, Union, Any
from PySide6.QtCore import QStandardPaths
from PySide6.QtGui import QPixmap, QIcon
from PySide6.QtWidgets import QApplication

from .logger import logger


def get_app_data_dir() -> Path:
    """获取应用程序数据目录"""
    try:
        app_data = QStandardPaths.writableLocation(QStandardPaths.StandardLocation.AppDataLocation)
        if not app_data:
            # 备用方案
            app_data = os.path.expanduser("~/.myapp")
        
        data_dir = Path(app_data)
        data_dir.mkdir(parents=True, exist_ok=True)
        return data_dir
    except Exception as e:
        logger.error(f"获取应用数据目录失败: {e}")
        return Path.cwd()


def get_resource_path(relative_path: str) -> Path:
    """获取资源文件路径"""
    try:
        # 获取项目根目录
        if getattr(sys, 'frozen', False):
            # 打包后的可执行文件
            base_path = Path(sys.executable).parent
        else:
            # 开发环境
            base_path = Path(__file__).parent.parent.parent
        
        resource_path = base_path / "resources" / relative_path
        return resource_path
    except Exception as e:
        logger.error(f"获取资源路径失败: {e}")
        return Path(relative_path)


def load_icon(icon_path: str, size: tuple = None) -> QIcon:
    """加载图标"""
    try:
        if not icon_path:
            return QIcon()
        
        # 尝试不同的路径
        paths_to_try = [
            Path(icon_path),
            get_resource_path(f"icons/{icon_path}"),
            get_resource_path(icon_path)
        ]
        
        for path in paths_to_try:
            if path.exists():
                pixmap = QPixmap(str(path))
                if not pixmap.isNull():
                    if size:
                        pixmap = pixmap.scaled(size[0], size[1])
                    return QIcon(pixmap)
        
        # 如果找不到图标，返回默认图标
        logger.warning(f"图标文件不存在: {icon_path}")
        return QIcon()
        
    except Exception as e:
        logger.error(f"加载图标失败: {e}")
        return QIcon()


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    try:
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    except Exception as e:
        logger.error(f"格式化文件大小失败: {e}")
        return "未知"


def is_text_file(file_path: Union[str, Path]) -> bool:
    """检查是否为文本文件"""
    try:
        file_path = Path(file_path)
        
        # 检查文件扩展名
        text_extensions = {
            '.txt', '.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml',
            '.md', '.rst', '.ini', '.cfg', '.conf', '.log', '.csv', '.sql', '.sh',
            '.bat', '.ps1', '.c', '.cpp', '.h', '.hpp', '.java', '.cs', '.php',
            '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.r', '.m', '.pl'
        }
        
        if file_path.suffix.lower() in text_extensions:
            return True
        
        # 尝试读取文件开头判断
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                if b'\0' in chunk:
                    return False  # 包含空字节，可能是二进制文件
                
                # 尝试解码为文本
                try:
                    chunk.decode('utf-8')
                    return True
                except UnicodeDecodeError:
                    try:
                        chunk.decode('gbk')
                        return True
                    except UnicodeDecodeError:
                        return False
        except:
            return False
            
    except Exception as e:
        logger.error(f"检查文本文件失败: {e}")
        return False


def safe_cast(value: Any, target_type: type, default: Any = None):
    """安全类型转换"""
    try:
        return target_type(value)
    except (ValueError, TypeError):
        return default


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """截断文本"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def ensure_directory(directory: Union[str, Path]) -> bool:
    """确保目录存在"""
    try:
        Path(directory).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败 {directory}: {e}")
        return False


def get_file_info(file_path: Union[str, Path]) -> dict:
    """获取文件信息"""
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            return {}
        
        stat = file_path.stat()
        return {
            'name': file_path.name,
            'size': stat.st_size,
            'size_formatted': format_file_size(stat.st_size),
            'modified': stat.st_mtime,
            'is_file': file_path.is_file(),
            'is_dir': file_path.is_dir(),
            'extension': file_path.suffix,
            'is_text': is_text_file(file_path) if file_path.is_file() else False
        }
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        return {}


def get_screen_geometry():
    """获取屏幕几何信息"""
    try:
        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            if screen:
                geometry = screen.geometry()
                return {
                    'width': geometry.width(),
                    'height': geometry.height(),
                    'dpi': screen.logicalDotsPerInch()
                }
    except Exception as e:
        logger.error(f"获取屏幕信息失败: {e}")
    
    return {'width': 1920, 'height': 1080, 'dpi': 96}


def calculate_dpi_scale() -> float:
    """计算DPI缩放比例"""
    try:
        screen_info = get_screen_geometry()
        dpi = screen_info.get('dpi', 96)
        return dpi / 96.0  # 96 DPI为基准
    except Exception as e:
        logger.error(f"计算DPI缩放失败: {e}")
        return 1.0
