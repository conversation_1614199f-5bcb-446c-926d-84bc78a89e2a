"""
配置管理模块
提供集中化的配置管理，支持动态配置更新
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union
from PySide6.QtCore import QObject, Signal

from ..utils.logger import logger


class ConfigManager(QObject):
    """配置管理器"""
    
    # 配置更新信号
    config_changed = Signal(str, object)  # (配置键, 新值)
    
    def __init__(self, config_dir: str = "config"):
        super().__init__()
        self.config_dir = Path(config_dir)
        self._configs: Dict[str, Dict] = {}
        self._config_files: Dict[str, Path] = {}
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 加载默认配置文件
        self._load_default_configs()
    
    def _load_default_configs(self):
        """加载默认配置文件"""
        default_files = {
            'app': 'app_config.json',
            'plugins': 'plugins_config.json'
        }
        
        for config_name, filename in default_files.items():
            config_path = self.config_dir / filename
            if config_path.exists():
                self.load_config(config_name, config_path)
            else:
                logger.warning(f"配置文件不存在: {config_path}")
                self._configs[config_name] = {}
    
    def load_config(self, name: str, file_path: Union[str, Path]) -> bool:
        """
        加载配置文件
        
        Args:
            name: 配置名称
            file_path: 配置文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            file_path = Path(file_path)
            self._config_files[name] = file_path
            
            if not file_path.exists():
                logger.warning(f"配置文件不存在: {file_path}")
                self._configs[name] = {}
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                self._configs[name] = config_data
                logger.info(f"已加载配置文件: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"加载配置文件失败 {file_path}: {e}")
            self._configs[name] = {}
            return False
    
    def save_config(self, name: str, file_path: Optional[Union[str, Path]] = None) -> bool:
        """
        保存配置文件
        
        Args:
            name: 配置名称
            file_path: 配置文件路径（可选，默认使用加载时的路径）
            
        Returns:
            bool: 是否保存成功
        """
        try:
            if file_path is None:
                file_path = self._config_files.get(name)
                if file_path is None:
                    logger.error(f"未找到配置 {name} 的文件路径")
                    return False
            else:
                file_path = Path(file_path)
                self._config_files[name] = file_path
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self._configs.get(name, {}), f, 
                         ensure_ascii=False, indent=2)
                logger.info(f"已保存配置文件: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"保存配置文件失败 {file_path}: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键（如 'app.window.width'）
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            config_name = keys[0]
            
            if config_name not in self._configs:
                return default
            
            value = self._configs[config_name]
            for k in keys[1:]:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            logger.error(f"获取配置值失败 {key}: {e}")
            return default
    
    def set(self, key: str, value: Any, save: bool = True) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            save: 是否立即保存到文件
            
        Returns:
            bool: 是否设置成功
        """
        try:
            keys = key.split('.')
            config_name = keys[0]
            
            # 确保配置存在
            if config_name not in self._configs:
                self._configs[config_name] = {}
            
            # 设置嵌套值
            config = self._configs[config_name]
            for k in keys[1:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置最终值
            if len(keys) > 1:
                config[keys[-1]] = value
            else:
                self._configs[config_name] = value
            
            # 发送配置更新信号
            self.config_changed.emit(key, value)
            
            # 保存到文件
            if save:
                self.save_config(config_name)
            
            logger.debug(f"已设置配置: {key} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"设置配置值失败 {key}: {e}")
            return False
    
    def get_config(self, name: str) -> Dict:
        """
        获取整个配置字典
        
        Args:
            name: 配置名称
            
        Returns:
            配置字典
        """
        return self._configs.get(name, {}).copy()
    
    def has_config(self, name: str) -> bool:
        """
        检查配置是否存在
        
        Args:
            name: 配置名称
            
        Returns:
            bool: 是否存在
        """
        return name in self._configs
    
    def reload_config(self, name: str) -> bool:
        """
        重新加载配置文件
        
        Args:
            name: 配置名称
            
        Returns:
            bool: 是否重新加载成功
        """
        if name in self._config_files:
            return self.load_config(name, self._config_files[name])
        return False
    
    def get_all_configs(self) -> Dict[str, Dict]:
        """获取所有配置"""
        return {name: config.copy() for name, config in self._configs.items()}


# 全局配置管理器实例
config_manager = ConfigManager()
