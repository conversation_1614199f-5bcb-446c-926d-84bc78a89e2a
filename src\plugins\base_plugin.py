"""
插件基类模块
定义插件的基础接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Optional, Any, Dict, List
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import QObject, Signal, QMetaObject
from PySide6.QtCore import QAbstractItemModel

from ..core.plugin_manager import PluginInfo, PluginState
from ..core.event_bus import event_bus
from ..core.config_manager import config_manager
from ..utils.logger import logger


# 解决元类冲突的方法
class PluginMeta(type(QObject), type(ABC)):
    pass


class BasePlugin(QObject, ABC, metaclass=PluginMeta):
    """插件基类"""
    
    # 插件信号
    status_changed = Signal(str)  # 状态变化
    error_occurred = Signal(str)  # 错误发生
    
    def __init__(self):
        super().__init__()
        
        # 插件基本信息
        self._info: Optional[PluginInfo] = None
        self._manager = None
        self._active = False
        self._initialized = False
        
        # UI组件
        self._sidebar_widget: Optional[QWidget] = None
        self._workspace_widget: Optional[QWidget] = None
        
        # 配置和状态
        self._config_key = ""
        self._plugin_config: Dict = {}
        
        # 初始化插件
        self._initialize_plugin()
    
    def _initialize_plugin(self):
        """初始化插件"""
        try:
            # 获取插件信息
            self._info = self.get_info()
            if self._info:
                self._config_key = f"plugins.{self._info.name.lower()}"
                self._load_plugin_config()
            
            self._initialized = True
            logger.debug(f"插件初始化完成: {self.get_name()}")
            
        except Exception as e:
            logger.error(f"插件初始化失败: {e}")
            self._initialized = False
    
    @abstractmethod
    def get_info(self) -> PluginInfo:
        """
        获取插件信息
        
        Returns:
            PluginInfo: 插件信息对象
        """
        pass
    
    @abstractmethod
    def activate(self) -> bool:
        """
        激活插件
        
        Returns:
            bool: 是否激活成功
        """
        pass
    
    @abstractmethod
    def deactivate(self) -> bool:
        """
        停用插件
        
        Returns:
            bool: 是否停用成功
        """
        pass
    
    def create_sidebar_widget(self) -> Optional[QWidget]:
        """
        创建侧边栏组件（可选实现）
        
        Returns:
            QWidget: 侧边栏组件，如果不需要则返回None
        """
        return None
    
    def create_workspace_widget(self) -> Optional[QWidget]:
        """
        创建工作区组件（可选实现）
        
        Returns:
            QWidget: 工作区组件，如果不需要则返回None
        """
        return None
    
    def get_widget(self) -> Optional[QWidget]:
        """
        获取插件的主要UI组件（通常是侧边栏组件）
        
        Returns:
            QWidget: 插件UI组件
        """
        if self._sidebar_widget is None:
            self._sidebar_widget = self.create_sidebar_widget()
        return self._sidebar_widget
    
    def get_workspace_widget(self) -> Optional[QWidget]:
        """
        获取插件的工作区组件
        
        Returns:
            QWidget: 工作区组件
        """
        if self._workspace_widget is None:
            self._workspace_widget = self.create_workspace_widget()
        return self._workspace_widget
    
    def get_icon(self) -> str:
        """
        获取插件图标路径
        
        Returns:
            str: 图标路径
        """
        if self._info:
            return self._info.icon
        return ""
    
    def get_name(self) -> str:
        """
        获取插件名称
        
        Returns:
            str: 插件名称
        """
        if self._info:
            return self._info.name
        return self.__class__.__name__
    
    def get_description(self) -> str:
        """
        获取插件描述
        
        Returns:
            str: 插件描述
        """
        if self._info:
            return self._info.description
        return ""
    
    def get_version(self) -> str:
        """
        获取插件版本
        
        Returns:
            str: 插件版本
        """
        if self._info:
            return self._info.version
        return "1.0.0"
    
    def is_active(self) -> bool:
        """
        检查插件是否激活
        
        Returns:
            bool: 是否激活
        """
        return self._active
    
    def is_initialized(self) -> bool:
        """
        检查插件是否已初始化
        
        Returns:
            bool: 是否已初始化
        """
        return self._initialized
    
    def set_manager(self, manager):
        """
        设置插件管理器引用
        
        Args:
            manager: 插件管理器实例
        """
        self._manager = manager
    
    def get_manager(self):
        """
        获取插件管理器引用
        
        Returns:
            插件管理器实例
        """
        return self._manager
    
    def _load_plugin_config(self):
        """加载插件配置"""
        try:
            if self._config_key:
                self._plugin_config = config_manager.get(self._config_key, {})
                logger.debug(f"插件配置加载完成: {self.get_name()}")
        except Exception as e:
            logger.error(f"加载插件配置失败: {e}")
            self._plugin_config = {}
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取插件配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self._plugin_config.get(key, default)
    
    def set_config(self, key: str, value: Any, save: bool = True):
        """
        设置插件配置值
        
        Args:
            key: 配置键
            value: 配置值
            save: 是否立即保存
        """
        self._plugin_config[key] = value
        
        if save and self._config_key:
            config_manager.set(self._config_key, self._plugin_config)
    
    def emit_event(self, event_name: str, data: Any = None):
        """
        发送事件
        
        Args:
            event_name: 事件名称
            data: 事件数据
        """
        event_bus.emit(event_name, data, source=self.get_name())
    
    def subscribe_event(self, event_name: str, callback):
        """
        订阅事件
        
        Args:
            event_name: 事件名称
            callback: 回调函数
        """
        event_bus.subscribe(event_name, callback)
    
    def unsubscribe_event(self, event_name: str, callback):
        """
        取消订阅事件
        
        Args:
            event_name: 事件名称
            callback: 回调函数
        """
        event_bus.unsubscribe(event_name, callback)
    
    def log_info(self, message: str):
        """记录信息日志"""
        logger.info(f"[{self.get_name()}] {message}")
    
    def log_warning(self, message: str):
        """记录警告日志"""
        logger.warning(f"[{self.get_name()}] {message}")
    
    def log_error(self, message: str):
        """记录错误日志"""
        logger.error(f"[{self.get_name()}] {message}")
    
    def log_debug(self, message: str):
        """记录调试日志"""
        logger.debug(f"[{self.get_name()}] {message}")
    
    def _set_active(self, active: bool):
        """
        设置激活状态（内部方法）
        
        Args:
            active: 激活状态
        """
        if self._active != active:
            self._active = active
            status = "激活" if active else "停用"
            self.status_changed.emit(f"插件{status}")
            self.log_info(f"插件{status}")
    
    def _handle_error(self, error_message: str):
        """
        处理插件错误（内部方法）
        
        Args:
            error_message: 错误信息
        """
        self.error_occurred.emit(error_message)
        self.log_error(error_message)
    
    def on_activate(self):
        """
        激活时的回调方法（可选重写）
        在activate()方法成功执行后调用
        """
        pass
    
    def on_deactivate(self):
        """
        停用时的回调方法（可选重写）
        在deactivate()方法成功执行后调用
        """
        pass
    
    def on_config_changed(self, key: str, value: Any):
        """
        配置变化时的回调方法（可选重写）
        
        Args:
            key: 配置键
            value: 新值
        """
        pass
    
    def cleanup(self):
        """
        清理资源（可选重写）
        在插件被卸载时调用
        """
        try:
            # 清理UI组件
            if self._sidebar_widget:
                self._sidebar_widget.deleteLater()
                self._sidebar_widget = None
            
            if self._workspace_widget:
                self._workspace_widget.deleteLater()
                self._workspace_widget = None
            
            # 保存配置
            if self._config_key and self._plugin_config:
                config_manager.set(self._config_key, self._plugin_config)
            
            self.log_debug("插件资源清理完成")
            
        except Exception as e:
            self.log_error(f"清理插件资源失败: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass
