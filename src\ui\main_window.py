"""
主窗口模块
应用程序的主窗口，管理整体布局和组件
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, 
                               QVBoxLayout, QStatusBar, QLabel)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QCloseEvent

from .components.resizable_splitter import ResizableSplitter
from .sidebar import Sidebar
from .workspace import Workspace
from ..core.config_manager import config_manager
from ..core.event_bus import event_bus
from ..core.plugin_manager import plugin_manager
from ..utils.logger import logger


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 窗口信号
    window_closing = Signal()
    layout_changed = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 组件引用
        self._central_widget: QWidget = None
        self._main_splitter: ResizableSplitter = None
        self._sidebar: Sidebar = None
        self._workspace: Workspace = None
        self._status_bar: QStatusBar = None
        
        # 状态变量
        self._initialized = False
        self._closing = False
        
        # 初始化UI
        self._init_ui()
        
        # 连接事件
        self._connect_events()
        
        # 恢复窗口状态
        self._restore_window_state()
        
        logger.info("主窗口创建完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        try:
            # 设置窗口属性
            self.setObjectName("mainWindow")
            self.setWindowTitle("MyApp")
            
            # 创建中央组件
            self._central_widget = QWidget()
            self.setCentralWidget(self._central_widget)
            
            # 创建主布局
            main_layout = QHBoxLayout(self._central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # 创建主分割器
            self._main_splitter = ResizableSplitter(Qt.Orientation.Horizontal)
            main_layout.addWidget(self._main_splitter)
            
            # 创建侧边栏
            self._sidebar = Sidebar()
            self._main_splitter.addWidget(self._sidebar)
            
            # 创建工作区
            self._workspace = Workspace()
            self._main_splitter.addWidget(self._workspace)
            
            # 配置分割器
            self._setup_splitter()
            
            # 创建状态栏
            self._create_status_bar()
            
            self._initialized = True
            logger.debug("主窗口UI初始化完成")
            
        except Exception as e:
            logger.error(f"初始化主窗口UI失败: {e}")
    
    def _setup_splitter(self):
        """配置分割器"""
        try:
            # 获取配置
            sidebar_config = config_manager.get("app.sidebar", {})
            icon_width = sidebar_config.get("icon_width", 50)
            min_content_width = sidebar_config.get("min_content_width", 125)
            default_width = sidebar_config.get("default_width", 300)
            
            # 设置侧边栏配置
            self._main_splitter.set_panel_config(
                index=0,
                min_size=icon_width,
                collapse_threshold=min_content_width,
                default_size=default_width
            )
            
            # 设置工作区配置
            self._main_splitter.set_panel_config(
                index=1,
                min_size=400,
                collapse_threshold=200,
                default_size=800
            )
            
            # 设置初始尺寸
            self._main_splitter.setSizes([default_width, 800])
            
            # 连接分割器信号
            self._main_splitter.panel_collapsed.connect(self._on_panel_collapsed)
            self._main_splitter.panel_expanded.connect(self._on_panel_expanded)
            self._main_splitter.sizes_changed.connect(self._on_sizes_changed)
            
            # 设置折叠/展开回调
            self._main_splitter.set_collapse_callback(0, self._sidebar.collapse)
            self._main_splitter.set_expand_callback(0, self._sidebar.expand)
            
            logger.debug("分割器配置完成")
            
        except Exception as e:
            logger.error(f"配置分割器失败: {e}")
    
    def _create_status_bar(self):
        """创建状态栏"""
        try:
            self._status_bar = QStatusBar()
            self.setStatusBar(self._status_bar)
            
            # 添加状态标签
            self._status_label = QLabel("就绪")
            self._status_bar.addWidget(self._status_label)
            
            # 添加插件状态
            self._plugin_status_label = QLabel()
            self._status_bar.addPermanentWidget(self._plugin_status_label)
            
            # 更新插件状态
            self._update_plugin_status()
            
            logger.debug("状态栏创建完成")
            
        except Exception as e:
            logger.error(f"创建状态栏失败: {e}")
    
    def _connect_events(self):
        """连接事件信号"""
        try:
            # 连接插件管理器信号
            plugin_manager.plugin_activated.connect(self._on_plugin_activated)
            plugin_manager.plugin_deactivated.connect(self._on_plugin_deactivated)
            plugin_manager.plugin_error.connect(self._on_plugin_error)
            
            # 连接侧边栏信号
            if self._sidebar:
                self._sidebar.content_changed.connect(self._on_sidebar_content_changed)
                self._sidebar.collapsed_state_changed.connect(self._on_sidebar_collapsed_changed)
            
            # 连接工作区信号
            if self._workspace:
                self._workspace.tab_changed.connect(self._on_workspace_tab_changed)
            
            # 订阅全局事件
            event_bus.subscribe("app_status_changed", self._on_app_status_changed)
            event_bus.subscribe("plugin_status_changed", self._on_plugin_status_changed)
            
            logger.debug("事件连接完成")
            
        except Exception as e:
            logger.error(f"连接事件失败: {e}")
    
    def _on_panel_collapsed(self, index: int):
        """处理面板折叠事件"""
        if index == 0:  # 侧边栏
            logger.debug("侧边栏已折叠")
            self._update_status("侧边栏已折叠")
    
    def _on_panel_expanded(self, index: int):
        """处理面板展开事件"""
        if index == 0:  # 侧边栏
            logger.debug("侧边栏已展开")
            self._update_status("侧边栏已展开")
    
    def _on_sizes_changed(self, sizes: list):
        """处理尺寸变化事件"""
        # 保存布局状态
        QTimer.singleShot(500, self._save_layout_state)
        self.layout_changed.emit()
    
    def _on_plugin_activated(self, plugin_name: str):
        """处理插件激活事件"""
        self._update_status(f"插件已激活: {plugin_name}")
        self._update_plugin_status()
        
        # 通知侧边栏更新
        if self._sidebar:
            self._sidebar.refresh_plugins()
    
    def _on_plugin_deactivated(self, plugin_name: str):
        """处理插件停用事件"""
        self._update_status(f"插件已停用: {plugin_name}")
        self._update_plugin_status()
        
        # 通知侧边栏更新
        if self._sidebar:
            self._sidebar.refresh_plugins()
    
    def _on_plugin_error(self, plugin_name: str, error_msg: str):
        """处理插件错误事件"""
        self._update_status(f"插件错误: {plugin_name} - {error_msg}")
        self._update_plugin_status()
    
    def _on_sidebar_content_changed(self, plugin_name: str):
        """处理侧边栏内容变化事件"""
        self._update_status(f"切换到: {plugin_name}")
        
        # 通知工作区更新
        if self._workspace:
            self._workspace.set_active_plugin(plugin_name)
    
    def _on_sidebar_collapsed_changed(self, collapsed: bool):
        """处理侧边栏折叠状态变化事件"""
        state = "折叠" if collapsed else "展开"
        self._update_status(f"侧边栏{state}")
    
    def _on_workspace_tab_changed(self, tab_name: str):
        """处理工作区标签变化事件"""
        self._update_status(f"当前标签: {tab_name}")
    
    def _on_app_status_changed(self, event):
        """处理应用状态变化事件"""
        if hasattr(event, 'data') and event.data:
            self._update_status(str(event.data))
    
    def _on_plugin_status_changed(self, event):
        """处理插件状态变化事件"""
        self._update_plugin_status()
    
    def _update_status(self, message: str):
        """更新状态栏消息"""
        if self._status_label:
            self._status_label.setText(message)
            logger.debug(f"状态更新: {message}")
    
    def _update_plugin_status(self):
        """更新插件状态显示"""
        try:
            active_plugins = plugin_manager.get_active_plugins()
            plugin_count = len(active_plugins)
            
            if self._plugin_status_label:
                self._plugin_status_label.setText(f"插件: {plugin_count}")
                
        except Exception as e:
            logger.error(f"更新插件状态失败: {e}")
    
    def _save_layout_state(self):
        """保存布局状态"""
        try:
            if self._main_splitter:
                self._main_splitter.save_state("app.layout.main_splitter")
            
            # 保存窗口几何信息
            geometry = {
                "x": self.x(),
                "y": self.y(),
                "width": self.width(),
                "height": self.height(),
                "maximized": self.isMaximized()
            }
            config_manager.set("app.window.geometry", geometry)
            
        except Exception as e:
            logger.error(f"保存布局状态失败: {e}")
    
    def _restore_window_state(self):
        """恢复窗口状态"""
        try:
            # 恢复窗口几何信息
            geometry = config_manager.get("app.window.geometry")
            if geometry and isinstance(geometry, dict):
                self.setGeometry(
                    geometry.get("x", 100),
                    geometry.get("y", 100),
                    geometry.get("width", 1200),
                    geometry.get("height", 800)
                )
                
                if geometry.get("maximized", False):
                    self.showMaximized()
            
            # 恢复分割器状态
            if self._main_splitter:
                self._main_splitter.restore_state("app.layout.main_splitter")
            
            logger.debug("窗口状态恢复完成")
            
        except Exception as e:
            logger.error(f"恢复窗口状态失败: {e}")
    
    def get_sidebar(self) -> Sidebar:
        """获取侧边栏"""
        return self._sidebar
    
    def get_workspace(self) -> Workspace:
        """获取工作区"""
        return self._workspace
    
    def get_main_splitter(self) -> ResizableSplitter:
        """获取主分割器"""
        return self._main_splitter
    
    def toggle_sidebar(self):
        """切换侧边栏显示状态"""
        if self._main_splitter:
            self._main_splitter.toggle_panel(0)
    
    def closeEvent(self, event: QCloseEvent):
        """处理窗口关闭事件"""
        if self._closing:
            event.accept()
            return
        
        try:
            self._closing = True
            
            # 发送关闭信号
            self.window_closing.emit()
            
            # 保存状态
            self._save_layout_state()
            
            # 通知其他组件
            event_bus.emit("main_window_closing", immediate=True)
            
            logger.info("主窗口正在关闭")
            event.accept()
            
        except Exception as e:
            logger.error(f"关闭主窗口失败: {e}")
            event.accept()
